{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "reset-db": "node scripts/reset-db.js", "docker:build": "docker build -t my-v0-project .", "lint": "next lint"}, "dependencies": {"@11labs/react": "0.1.4", "@ai-sdk/openai": "^1.3.22", "@copilotkit/react-core": "^1.9.0", "@copilotkit/react-ui": "^1.9.0", "@emotion/is-prop-valid": "1.3.1", "@expo/metro-runtime": "~5.0.4", "@hookform/resolvers": "^3.10.0", "@livekit/components-react": "2.9.3", "@neondatabase/serverless": "1.0.0", "@number-flow/react": "^0.5.10", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "ai": "3.4.33", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.6.0", "dotenv": "16.5.0", "embla-carousel-react": "8.5.1", "expo": "^53.0.11", "framer-motion": "12.10.5", "input-otp": "1.4.1", "livekit-client": "^2.13.5", "livekit-server-sdk": "2.12.0", "lucide": "^0.509.0", "lucide-react": "^0.509.0", "motion": "^12.18.1", "next": "^15.3.3", "next-pwa": "^5.6.0", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "nodemailer": "^6.10.1", "openai": "4.98.0", "orate": "^2.0.6", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.0", "react-native-web": "^0.20.0", "react-resizable-panels": "^2.1.9", "recharts": "2.15.0", "sonner": "2.0.3", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@types/node": "^22.15.32", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.29.0", "eslint-config-next": "^15.3.3", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}, "packageManager": "pnpm@10.12.1"}