# Voice Assistant Deployment Guide

This guide explains how to deploy the voice assistant application using Docker.

## Prerequisites

- Docker and Docker Compose installed
- Git
- A container registry account (GitHub Container Registry, Docker Hub, etc.)

## Configuration

1. Create a `.env` file with your environment variables:

```
# Docker deployment configuration
REGISTRY=ghcr.io/your-username
TAG=latest
WEB_PORT=3000

# Database connection
DATABASE_URL=**************************************/dbname

# LiveKit configuration (for agent)
LIVEKIT_URL=wss://your-livekit-server.com
LIVEKIT_API_KEY=your_api_key
LIVEKIT_API_SECRET=your_api_secret

# Speech services
ELEVENLABS_API_KEY=your_elevenlabs_key
DEEPGRAM_API_KEY=your_deepgram_key
RIME_API_KEY=your_rime_key

# LLM services
OPENAI_API_KEY=your_openai_key
```

## Build and Push Docker Images

1. Make the build script executable:
```bash
chmod +x docker-build.sh
```

2. Build the images:
```bash
./docker-build.sh
```

3. Log in to your registry:
```bash
# For GitHub Container Registry
echo $GITHUB_TOKEN | docker login ghcr.io -u USERNAME --password-stdin

# For Docker Hub
docker login
```

4. Push the images:
```bash
# Set your registry in .env or override with environment variable
REGISTRY=ghcr.io/your-username ./docker-build.sh

# Push the images
docker push ${REGISTRY}/voice-assistant-web:${TAG}
docker push ${REGISTRY}/voice-assistant-agent:${TAG}
```

## Deployment Options

### Local Deployment

For local testing with Docker:

```bash
docker-compose up
```

### Production Deployment

For production deployment:

```bash
# Make sure your .env file is configured correctly
docker-compose -f docker-compose.prod.yml up -d
```

### Self-hosting LiveKit Server

The `docker-compose.prod.yml` includes a commented section for self-hosting LiveKit.

1. Uncomment the LiveKit service section
2. Create a `livekit.yaml` configuration file:

```yaml
port: 7880
rtc:
  port_range_start: 50000
  port_range_end: 60000
keys:
  your_api_key: your_secret_key
```

3. Update your `.env` file to point to your self-hosted LiveKit:
```
LIVEKIT_URL=ws://livekit:7880
LIVEKIT_API_KEY=your_api_key
LIVEKIT_API_SECRET=your_secret_key
```

## Monitoring and Management

- View logs: `docker-compose -f docker-compose.prod.yml logs -f`
- Restart services: `docker-compose -f docker-compose.prod.yml restart`
- Stop services: `docker-compose -f docker-compose.prod.yml down`

## Security Considerations

- Use a proper database password
- Set up SSL/TLS for your LiveKit server
- Keep API keys secure
- Use network rules to restrict access to internal services 