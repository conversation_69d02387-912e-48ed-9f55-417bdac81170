"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import ErrorBoundary from "@/components/ErrorBoundary"
import LoginScreen from "@/components/LoginScreen"
import { v4 as uuidv4 } from "uuid"
import { toast, Toaster } from "sonner"
import { History } from "lucide-react"

export default function Home() {
  const [isDarkMode, setIsDarkMode] = useState(true)
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  const handleLogin = (email: string, password: string) => {
    try {
      console.log("Auto-login with:", email, password)
      setIsLoggedIn(true)

      // Generate a unique conversation ID
      const conversationId = uuidv4()

      // Redirect to the conversation page
      router.push(`/c/${conversationId}`)
    } catch (err) {
      console.error("Login error:", err)
      setError("Failed to initialize. Please try again.")
      setIsLoggedIn(false)
      toast.error("Failed to initialize. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    // Add a timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      if (isLoading) {
        setIsLoading(false)
        setError("Initialization timed out. Please refresh the page.")
        toast.error("Initialization timed out. Please refresh the page.")
      }
    }, 10000)

    // Skip auto-login for security - require user to enter credentials
    const initTimeout = setTimeout(() => {
      setIsLoading(false)
    }, 1000)

    return () => {
      clearTimeout(timeoutId)
      clearTimeout(initTimeout)
    }
  }, [])

  return (
    <ErrorBoundary>
      <div className={isDarkMode ? "dark" : ""}>
        <Toaster position="top-center" />
        {error && (
          <div className="fixed top-0 left-0 right-0 bg-red-500 text-white p-2 text-center text-sm z-50">
            Error: {error}
          </div>
        )}

        {isLoading ? (
          <div className="flex flex-col items-center justify-center h-screen bg-zinc-900">
            <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
            <p className="text-white text-xl">Initializing application...</p>
          </div>
        ) : !isLoggedIn ? (
          <LoginScreen onLogin={handleLogin} />
        ) : (
          <div className="flex items-center justify-center h-screen bg-zinc-900">
            <p className="text-white text-xl">Redirecting to conversation...</p>
          </div>
        )}

        {/* History button */}
        {isLoggedIn && !isLoading && (
          <button
            onClick={() => router.push("/history")}
            className="fixed bottom-6 right-6 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg transition-all"
            aria-label="View conversation history"
          >
            <History className="w-6 h-6" />
          </button>
        )}
      </div>
    </ErrorBoundary>
  )
}

