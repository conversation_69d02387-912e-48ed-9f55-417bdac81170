"use client"

import { useState } from "react"
import AiTalkingAnimation from "@/components/TextAnimation"
import { executeToolCall } from "@/lib/clientTools"

export default function DemoToolsPage() {
  const [demoText, setDemoText] = useState("")
  
  const handleTest = (toolName: string) => {
    setDemoText(`Testing ${toolName}...`)
    
    if (toolName === 'weather') {
      executeToolCall({
        name: 'show_weather',
        arguments: { city: 'Sydney' }
      })
    } else if (toolName === 'inspection') {
      executeToolCall({
        name: 'show_inspection_rating',
        arguments: { suburb: 'Subiaco' }
      })
    }
  }

  return (
    <div className="flex flex-col min-h-screen bg-zinc-950">
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold text-white mb-8">Voice Assistant Tools Demo</h1>
        
        <div className="bg-zinc-900 rounded-xl p-6 mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Test Individual Tools</h2>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={() => handleTest('weather')}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Test Weather Card
            </button>
            <button
              onClick={() => handleTest('inspection')}
              className="px-4 py-2 bg-violet-600 text-white rounded-lg hover:bg-violet-700 transition-colors"
            >
              Test Inspection Rating Card
            </button>
          </div>
        </div>
        
        <div className="bg-zinc-900 rounded-xl p-6 mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Test Streaming API</h2>
          <div className="flex flex-wrap gap-4">
            <button
              onClick={() => setDemoText("Using streaming API...")}
              className="px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
            >
              Start Streaming Demo
            </button>
          </div>
        </div>
      </div>
      
      <div className="flex-1 bg-zinc-900 border-t border-zinc-800">
        <div className="container mx-auto h-full max-w-3xl">
          <AiTalkingAnimation
            useStreamingApi={true}
            currentText={demoText}
            messages={[]}
            floatAtBottom={true}
          />
        </div>
      </div>
    </div>
  )
}
