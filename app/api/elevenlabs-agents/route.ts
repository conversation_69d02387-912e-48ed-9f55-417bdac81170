export const runtime = "edge"
export const dynamic = "force-dynamic"
export const fetchCache = "force-no-store"

import { NextResponse } from "next/server"

export async function GET(request: Request) {
  try {
    const apiKey = process.env.XI_API_KEY
    if (!apiKey) {
      console.error("XI_API_KEY environment variable not found")
      return NextResponse.json({ error: "XI_API_KEY is not set" }, { status: 500 })
    }

    // Current agent ID from environment (default selection)
    const currentAgentId = process.env.AGENT_ID

    // For this example, we'll use a list of hardcoded agents
    // In a production app, you would fetch this from the ElevenLabs API
    // ElevenLabs doesn't have a public API to list all available agents yet
    // So we're using this approach as a workaround
    const availableAgents = [
      {
        id: process.env.AGENT_ID || "",
        name: "Default Assistant",
        description: "The default voice assistant",
        isDefault: true
      },
      // Add more agents here if you have their IDs
      // {
      //   id: "another-agent-id", 
      //   name: "Customer Support Agent",
      //   description: "Specialized for customer inquiries",
      //   isDefault: false
      // },
    ]

    // Filter out any agents with empty IDs
    const validAgents = availableAgents.filter(agent => agent.id)

    if (validAgents.length === 0) {
      return NextResponse.json({ 
        error: "No valid agents found. Check your environment variables." 
      }, { status: 404 })
    }

    return NextResponse.json({ 
      agents: validAgents,
      currentAgentId 
    })
  } catch (error) {
    console.error("Error in /api/elevenlabs-agents route:", error)
    const message = error instanceof Error ? error.message : String(error)
    return NextResponse.json({ error: message }, { status: 500 })
  }
}