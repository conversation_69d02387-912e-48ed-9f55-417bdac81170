export const runtime = "edge"
export const dynamic = "force-dynamic"

import { neon, neonConfig } from "@neondatabase/serverless"
import { NextResponse } from "next/server"

// Configure neon to use fetch for pooling
;(neonConfig as any).poolQueryViaFetch = true
// Add a reasonable timeout
;(neonConfig as any).fetchTimeout = 10000
// Add backoff for rate limiting
;(neonConfig as any).fetchRetry = 3
;(neonConfig as any).fetchRetryMaxDelay = 2000

export async function GET() {
  if (!process.env.DATABASE_URL) {
    return NextResponse.json({ error: "DATABASE_URL environment variable not found." }, { status: 500 })
  }

  try {
    // Create a new neon client
    const sql = neon(process.env.DATABASE_URL)

    // Run a simple query to test the connection
    try {
      const resultRes = (await sql.query("SELECT 1 as test")) as any
      const result = resultRes.rows

      return NextResponse.json({
        success: true,
        message: "Database connection successful",
        result: result,
      })
    } catch (queryError) {
      // Convert the error to a string to check for rate limiting
      const errorString = String(queryError)

      // Check for rate limiting errors
      if (
        errorString.includes("Too Many Requests") ||
        errorString.includes("429") ||
        errorString.includes("rate limit")
      ) {
        console.error("Rate limit exceeded:", errorString)
        return new Response("Database rate limit exceeded. Please try again later.", {
          status: 429,
          headers: {
            "Content-Type": "text/plain",
          },
        })
      }

      // Handle other query errors
      console.error("Database query error:", errorString)
      return new Response(`Database query failed: ${errorString}`, {
        status: 500,
        headers: {
          "Content-Type": "text/plain",
        },
      })
    }
  } catch (error) {
    // Handle connection errors
    const errorString = String(error)
    console.error("Database connection error:", errorString)

    return new Response(`Database connection failed: ${errorString}`, {
      status: 500,
      headers: {
        "Content-Type": "text/plain",
      },
    })
  }
}
