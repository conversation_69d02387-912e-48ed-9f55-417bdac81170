import { transcribe } from 'ai';
import { openai } from '@ai-sdk/openai';

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const audio = formData.get('audio') as Blob;
    
    if (!audio) {
      return new Response('No audio data provided', { status: 400 });
    }

    const result = await transcribe({
      model: openai.transcription('whisper-1'),
      audio,
    });

    return Response.json({ text: result.text });
  } catch (error) {
    console.error('Transcription error:', error);
    return new Response('Failed to transcribe audio', { status: 500 });
  }
}