export const runtime = "edge"
export const dynamic = "force-dynamic"
export const fetchCache = "force-no-store"

import { NextResponse } from "next/server"

export async function POST(request: Request) {
  try {
    const agentId = process.env.AGENT_ID
    if (!agentId) {
      console.error("AGENT_ID environment variable not found")
      return NextResponse.json({ error: "AGENT_ID is not set or received." }, { status: 500 })
    }

    const apiKey = process.env.XI_API_KEY
    if (!apiKey) {
      console.error("XI_API_KEY environment variable not found")
      return NextResponse.json({ error: "XI_API_KEY is not set or received." }, { status: 500 })
    }

    // Create URL properly
    const apiUrl = new URL("https://api.elevenlabs.io/v1/convai/conversation/get_signed_url")
    apiUrl.searchParams.append("agent_id", agentId)

    console.log("Requesting signed URL from ElevenLabs:", apiUrl.toString())

    try {
      console.log("Making request to ElevenLabs API with agent_id:", agentId)

      const response = await fetch(apiUrl.toString(), {
        method: "GET",
        headers: {
          "xi-api-key": apiKey,
          "Content-Type": "application/json",
        },
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error("ElevenLabs API error:", errorText)

        // Include more detailed error information
        return NextResponse.json(
          {
            error: `ElevenLabs API error: ${response.status} ${response.statusText}`,
            details: errorText,
            requestUrl: apiUrl.toString(),
          },
          { status: response.status },
        )
      }

      const data = await response.json()
      console.log("ElevenLabs API response:", JSON.stringify(data, null, 2))

      if (!data.signed_url) {
        console.error("Missing signed_url in response:", data)
        return NextResponse.json({
          error: "Invalid response from ElevenLabs API",
          details: "The response did not include a signed_url field",
          response: data
        }, { status: 500 })
      }

      console.log("Successfully obtained signed URL from ElevenLabs")
      // Return the signed URL with the correct property name
      return NextResponse.json({ apiKey: data.signed_url, signedUrl: data.signed_url })
    } catch (fetchError) {
      console.error("Fetch error:", fetchError)
      return NextResponse.json(
        {
          error: `Error fetching from ElevenLabs API: ${fetchError instanceof Error ? fetchError.message : String(fetchError)}`,
        },
        { status: 500 },
      )
    }
  } catch (error) {
    console.error("Error in /api/i route:", error)
    const message = error instanceof Error ? error.message : String(error)
    return NextResponse.json({ error: message }, { status: 500 })
  }
}

