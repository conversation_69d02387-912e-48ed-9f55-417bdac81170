export const runtime = "edge"
export const dynamic = "force-dynamic"
export const fetchCache = "force-no-store"

import { NextResponse } from "next/server"
import {
  createMessage,
  getSessionMessages,
  updateMessage,
  deleteMessage,
  deleteSessionMessages,
  getSessionMessageCount,
  type Message,
} from "@/lib/db"

// Helper function to safely convert any error to a string
function errorToString(error: unknown): string {
  if (error === null) return "null"
  if (error === undefined) return "undefined"
  if (typeof error === "string") return error
  if (error instanceof Error) return error.message
  try {
    return JSON.stringify(error)
  } catch {
    return String(error)
  }
}

// Helper function to handle database errors
function handleDatabaseError(error: unknown) {
  const errorString = errorToString(error)

  // Check for rate limiting errors
  if (errorString.includes("Too Many Requests") || errorString.includes("429") || errorString.includes("rate limit")) {
    console.error("Rate limit exceeded:", errorString)
    return new Response("Database rate limit exceeded. Please try again later.", {
      status: 429,
      headers: {
        "Content-Type": "text/plain",
      },
    })
  }

  console.error("Database error:", errorString)
  return new Response(`Database operation failed: ${errorString}`, {
    status: 500,
    headers: {
      "Content-Type": "text/plain",
    },
  })
}

// Create a new message
export async function POST(request: Request) {
  try {
    const { id, item } = await request.json()

    if (!id || !item) {
      return NextResponse.json({ error: "Missing id or item" }, { status: 400 })
    }

    if (!process.env.DATABASE_URL) {
      return NextResponse.json({ error: "DATABASE_URL is not set" }, { status: 500 })
    }

    // Validate item structure
    if (!item.content || !Array.isArray(item.content) || !item.content[0]) {
      return NextResponse.json({ error: "Invalid item structure" }, { status: 400 })
    }

    // Get count for ordering
    let count = 0
    try {
      count = await getSessionMessageCount(id)
    } catch (countError) {
      console.error("Error getting message count:", countError)
      // Continue with count = 0
    }

    // Create message
    try {
      const message: Message = {
        created_at: count,
        id: item.id || `item_${Date.now()}`,
        session_id: id,
        content_type: item.content[0].type || "text",
        content_transcript: item.content[0].transcript || "",
        object: item.object || "realtime.item",
        role: item.role || "user",
        status: item.status || "completed",
        type: item.type || "message",
      }

      await createMessage(message)
      return NextResponse.json({ success: true })
    } catch (insertError) {
      return handleDatabaseError(insertError)
    }
  } catch (error) {
    console.error("Error in /api/c POST route:", error)
    return handleDatabaseError(error)
  }
}

// Get messages for a session
export async function GET(request: Request) {
  try {
    const url = new URL(request.url)
    const id = url.searchParams.get("id")

    if (!id) {
      return NextResponse.json([])
    }

    if (!process.env.DATABASE_URL) {
      return NextResponse.json({ error: "DATABASE_URL is not set" }, { status: 500 })
    }

    try {
      const messages = await getSessionMessages(id)
      return NextResponse.json(messages || [])
    } catch (queryError) {
      return handleDatabaseError(queryError)
    }
  } catch (error) {
    console.error("Error in /api/c GET route:", error)
    return handleDatabaseError(error)
  }
}

// Update a message
export async function PUT(request: Request) {
  try {
    const { id, updates } = await request.json()

    if (!id || !updates) {
      return NextResponse.json({ error: "Missing id or updates" }, { status: 400 })
    }

    if (!process.env.DATABASE_URL) {
      return NextResponse.json({ error: "DATABASE_URL is not set" }, { status: 500 })
    }

    try {
      const result = await updateMessage(id, updates)
      return NextResponse.json(result)
    } catch (updateError) {
      return handleDatabaseError(updateError)
    }
  } catch (error) {
    console.error("Error in /api/c PUT route:", error)
    return handleDatabaseError(error)
  }
}

// Delete a message or all messages for a session
export async function DELETE(request: Request) {
  try {
    const url = new URL(request.url)
    const id = url.searchParams.get("id")
    const sessionId = url.searchParams.get("sessionId")

    if (!id && !sessionId) {
      return NextResponse.json({ error: "Missing id or sessionId" }, { status: 400 })
    }

    if (!process.env.DATABASE_URL) {
      return NextResponse.json({ error: "DATABASE_URL is not set" }, { status: 500 })
    }

    try {
      if (sessionId) {
        // Delete all messages for a session
        const result = await deleteSessionMessages(sessionId)
        return NextResponse.json(result)
      } else if (id) {
        // Delete a specific message
        const result = await deleteMessage(id)
        return NextResponse.json(result)
      }
    } catch (deleteError) {
      return handleDatabaseError(deleteError)
    }
  } catch (error) {
    console.error("Error in /api/c DELETE route:", error)
    return handleDatabaseError(error)
  }
}

