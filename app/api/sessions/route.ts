export const runtime = "edge"
export const dynamic = "force-dynamic"
export const fetchCache = "force-no-store"

import { neon, neonConfig } from "@neondatabase/serverless"
import { NextResponse } from "next/server"

// Configure neon to use fetch for pooling
;(neonConfig as any).poolQueryViaFetch = true
;(neonConfig as any).fetchTimeout = 10000
;(neonConfig as any).fetchRetry = 3
;(neonConfig as any).fetchRetryMaxDelay = 2000

// Helper function to safely convert any error to a string
function errorToString(error: unknown): string {
  if (error === null) return "null"
  if (error === undefined) return "undefined"
  if (typeof error === "string") return error
  if (error instanceof Error) return error.message
  try {
    return JSON.stringify(error)
  } catch {
    return String(error)
  }
}

// Helper function to handle database errors
function handleDatabaseError(error: unknown) {
  const errorString = errorToString(error)

  if (errorString.includes("Too Many Requests") || errorString.includes("429") || errorString.includes("rate limit")) {
    console.error("Rate limit exceeded:", errorString)
    return new Response("Database rate limit exceeded. Please try again later.", {
      status: 429,
      headers: {
        "Content-Type": "text/plain",
      },
    })
  }

  console.error("Database error:", errorString)
  return new Response(`Database operation failed: ${errorString}`, {
    status: 500,
    headers: {
      "Content-Type": "text/plain",
    },
  })
}

// Get all unique session IDs
export async function GET() {
  if (!process.env.DATABASE_URL) {
    console.warn("DATABASE_URL environment variable not found")
    return NextResponse.json({
      sessions: [],
      error: "DATABASE_URL environment variable not found. Using in-memory storage.",
    })
  }

  try {
    const sql = neon(process.env.DATABASE_URL)

    try {
      // Get all unique session IDs with the timestamp of the most recent message
      console.log("Fetching sessions from database...");
      const sessionsRes = (await sql.query(
        `SELECT
          session_id,
          MAX(created_at) as last_activity,
          COUNT(*) as message_count
        FROM
          messages
        GROUP BY
          session_id
        ORDER BY
          last_activity DESC`,
      )) as any

      console.log("Sessions query result:", sessionsRes);

      // Ensure we always return an array for sessions, even if rows is undefined
      return NextResponse.json({ sessions: sessionsRes.rows || [] })
    } catch (dbError) {
      console.error("Database error when fetching sessions:", dbError)
      // Return empty sessions array instead of error response for better UX
      return NextResponse.json({ sessions: [], error: "Failed to fetch sessions from database" })
    }
  } catch (error) {
    console.error("Error getting sessions:", error)
    // Return empty sessions array instead of error response for better UX
    return NextResponse.json({ sessions: [], error: "Failed to connect to database" })
  }
}
