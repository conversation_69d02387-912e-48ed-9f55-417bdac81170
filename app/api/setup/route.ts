export const runtime = "edge"
export const dynamic = "force-dynamic"

import { neon, neonConfig } from "@neondatabase/serverless"
import { NextResponse } from "next/server"

// Configure neon to use fetch for pooling
;(neonConfig as any).poolQueryViaFetch = true
// Add a reasonable timeout
;(neonConfig as any).fetchTimeout = 10000
// Add backoff for rate limiting
;(neonConfig as any).fetchRetry = 3
;(neonConfig as any).fetchRetryMaxDelay = 2000

export async function GET() {
  if (!process.env.DATABASE_URL) {
    return NextResponse.json({ error: "DATABASE_URL environment variable not found." }, { status: 500 })
  }

  try {
    // Create a new neon client with proper connection handling
    const sql = neon(process.env.DATABASE_URL)

    // Use simpler queries and add error handling
    try {
      // Check if table exists using a simpler query
      const tableExistsResult = (await sql.query(
        "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)",
        ["messages"],
      )) as any

      const exists = tableExistsResult.rows?.[0]?.exists === true

      if (!exists) {
        // Create the messages table with separate statements to avoid template literal issues
        await sql.query(
          "CREATE TABLE IF NOT EXISTS messages (created_at INTEGER, id TEXT PRIMARY KEY, session_id TEXT NOT NULL, content_type TEXT, content_transcript TEXT, object TEXT, role TEXT NOT NULL, status TEXT, type TEXT)",
        )

        // Create an index for faster queries
        await sql.query("CREATE INDEX IF NOT EXISTS idx_session_created_at ON messages (session_id, created_at)")
      }

      return NextResponse.json({ success: true, message: "Schema setup completed successfully" })
    } catch (dbError) {
      // Convert the error to a string to check for rate limiting
      const errorString = String(dbError)

      // Check for rate limiting errors
      if (
        errorString.includes("Too Many Requests") ||
        errorString.includes("429") ||
        errorString.includes("rate limit")
      ) {
        console.error("Rate limit exceeded:", errorString)
        return new Response("Database rate limit exceeded. Please try again later.", {
          status: 429,
          headers: {
            "Content-Type": "text/plain",
          },
        })
      }

      // Handle other database errors
      console.error("Database operation error:", errorString)
      return new Response(`Database operation failed: ${errorString}`, {
        status: 500,
        headers: {
          "Content-Type": "text/plain",
        },
      })
    }
  } catch (error) {
    // Handle connection errors
    const errorString = String(error)
    console.error("Error setting up schema:", errorString)

    return new Response(`Schema setup failed: ${errorString}`, {
      status: 500,
      headers: {
        "Content-Type": "text/plain",
      },
    })
  }
}
