import type { NextRequest } from "next/server"
import { NextResponse } from "next/server"
// @ts-expect-error nodemailer types not installed
import nodemailer from "nodemailer"

// Create reusable transporter using SMTP transport
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: Number(process.env.SMTP_PORT || 587),
  secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
})

export async function POST(req: NextRequest) {
  try {
    const { to, cc, bcc, subject, body, priority } = await req.json()

    if (!to || !subject || !body) {
      return NextResponse.json({ success: false, error: 'Missing to, subject, or body' }, { status: 400 })
    }

    // Build email headers based on priority
    const headers: Record<string, string> = {}
    if (priority === 'high') {
      headers['X-Priority'] = '1'
      headers['X-MSMail-Priority'] = 'High'
      headers['Importance'] = 'high'
    } else if (priority === 'low') {
      headers['X-Priority'] = '5'
      headers['X-MSMail-Priority'] = 'Low'
      headers['Importance'] = 'low'
    }

    await transporter.sendMail({
      from: process.env.SMTP_FROM, // e.g. "<EMAIL>"
      to,
      cc: cc || undefined,
      bcc: bcc || undefined,
      subject,
      text: body.replace(/<[^>]*>/g, ''), // Strip HTML for text version
      html: body,
      headers,
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error sending email:', error)
    return NextResponse.json({ success: false, error: String(error) }, { status: 500 })
  }
} 