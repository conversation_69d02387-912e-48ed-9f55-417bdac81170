import { NextRequest, NextResponse } from "next/server"

export const runtime = "edge"
export const dynamic = "force-dynamic"

// MCP tool mapping - maps tool names to MCP server tool names
const MCP_TOOL_MAPPING: Record<string, { server: string; tool: string }> = {
  // ElevenLabs tools
  "text_to_speech": { server: "ElevenLabs", tool: "text_to_speech" },
  "speech_to_text": { server: "ElevenLabs", tool: "speech_to_text" },
  "text_to_sound_effects": { server: "ElevenLabs", tool: "text_to_sound_effects" },
  "search_voices": { server: "ElevenLabs", tool: "search_voices" },
  "get_voice": { server: "ElevenLabs", tool: "get_voice" },
  "voice_clone": { server: "ElevenLabs", tool: "voice_clone" },
  "isolate_audio": { server: "ElevenLabs", tool: "isolate_audio" },
  "create_agent": { server: "ElevenLabs", tool: "create_agent" },
  "list_agents": { server: "ElevenLabs", tool: "list_agents" },
  "get_agent": { server: "ElevenLabs", tool: "get_agent" },
  
  // Add more MCP tools here as needed
}

// Security token validation
function validateSecurityToken(request: NextRequest): boolean {
  const token = request.headers.get("x-mcp-security-token")
  const expectedToken = process.env.MCP_SECURITY_TOKEN
  
  // If no token is configured, allow all requests (development mode)
  if (!expectedToken) {
    console.warn("MCP_SECURITY_TOKEN not configured - accepting all requests")
    return true
  }
  
  return token === expectedToken
}

export async function POST(request: NextRequest) {
  try {
    // Validate security token
    if (!validateSecurityToken(request)) {
      return NextResponse.json(
        { error: "Unauthorized - Invalid security token" },
        { status: 401 }
      )
    }
    
    const { tool, args } = await request.json()
    
    if (!tool) {
      return NextResponse.json(
        { error: "Tool name is required" },
        { status: 400 }
      )
    }
    
    // Check if tool is mapped
    const toolMapping = MCP_TOOL_MAPPING[tool]
    if (!toolMapping) {
      return NextResponse.json(
        { error: `Unknown tool: ${tool}` },
        { status: 404 }
      )
    }
    
    // Since we're in an Edge runtime, we can't directly invoke MCP tools
    // Instead, we'll return a response indicating the tool would be called
    // In a real implementation, you would need to:
    // 1. Set up a separate Node.js server that can communicate with MCP servers
    // 2. Use WebSockets or Server-Sent Events to communicate with that server
    // 3. Or use a different runtime that supports the MCP protocol
    
    // For now, we'll return a mock response
    console.log(`MCP Bridge: Would invoke ${toolMapping.server}.${toolMapping.tool} with args:`, args)
    
    // Mock responses for common tools
    const mockResponses: Record<string, any> = {
      "text_to_speech": {
        success: true,
        file_path: "/tmp/audio_output.mp3",
        voice_used: "default"
      },
      "search_voices": {
        voices: [
          { id: "voice1", name: "Alice", language: "en" },
          { id: "voice2", name: "Bob", language: "en" }
        ]
      },
      "list_agents": {
        agents: [
          { id: "agent1", name: "Customer Support", status: "active" },
          { id: "agent2", name: "Sales Assistant", status: "active" }
        ]
      }
    }
    
    const mockResponse = mockResponses[tool] || {
      success: true,
      message: `Tool ${tool} executed successfully`,
      args: args
    }
    
    return NextResponse.json({
      success: true,
      tool: tool,
      server: toolMapping.server,
      data: mockResponse,
      note: "This is a mock response. In production, this would invoke the actual MCP tool."
    })
    
  } catch (error) {
    console.error("MCP Bridge error:", error)
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    )
  }
}

// GET endpoint to list available tools
export async function GET(request: NextRequest) {
  // Validate security token
  if (!validateSecurityToken(request)) {
    return NextResponse.json(
      { error: "Unauthorized - Invalid security token" },
      { status: 401 }
    )
  }
  
  const tools = Object.entries(MCP_TOOL_MAPPING).map(([name, mapping]) => ({
    name,
    server: mapping.server,
    mcpTool: mapping.tool
  }))
  
  return NextResponse.json({
    tools,
    totalTools: tools.length
  })
}