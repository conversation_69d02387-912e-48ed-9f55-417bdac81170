import { NextRequest, NextResponse } from 'next/server'
import { Readable } from 'stream'

// API route for streaming communication with ElevenLabs agent
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const message = searchParams.get('message')
  
  if (!message) {
    return NextResponse.json({ error: 'Message is required' }, { status: 400 })
  }

  // Setup Server-Sent Events response
  const encoder = new TextEncoder()
  const responseStream = new TransformStream()
  const writer = responseStream.writable.getWriter()

  // Helper function to send SSE events
  const sendEvent = async (event: string, data: string) => {
    await writer.write(
      encoder.encode(`event: ${event}\ndata: ${data}\n\n`)
    )
  }

  // Function to connect to ElevenLabs API and stream responses
  const streamElevenLabsResponse = async () => {
    try {
      // Realtime transcript simulation
      await sendEvent('transcript', message)
      
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // Analyze the message to determine what type of response to send
      const lowerCaseMessage = message.toLowerCase()
      
      if (lowerCaseMessage.includes('weather') || lowerCaseMessage.includes('temperature')) {
        // Extract city from message using simple pattern matching
        const cityMatch = lowerCaseMessage.match(/(weather|temperature)(?:\\s+in\\s+|\\s+for\\s+|\\s+at\\s+)?([a-z\\s]+)(?:$|[\\?\\.])/i)
        const city = cityMatch?.[2]?.trim() || 'your location'
        
        // Send a tool call for weather
        await sendEvent('tool_call', JSON.stringify({
          name: 'show_weather',
          arguments: { city }
        }))
        
        // Then send an assistant text response
        await sendEvent('assistant_text', `The current weather in ${city} is displayed on your screen.`)
      } 
      else if (lowerCaseMessage.includes('inspection') || lowerCaseMessage.includes('rating')) {
        // Extract suburb from message
        const suburbMatch = lowerCaseMessage.match(/(inspection|rating)(?:\\s+in\\s+|\\s+for\\s+|\\s+at\\s+)?([a-z\\s]+)(?:$|[\\?\\.])/i)
        const suburb = suburbMatch?.[2]?.trim() || 'your area'
        
        // Send a tool call for inspection rating
        await sendEvent('tool_call', JSON.stringify({
          name: 'show_inspection_rating',
          arguments: { suburb }
        }))
        
        // Then send an assistant text response
        await sendEvent('assistant_text', `I've pulled up the latest inspection ratings for ${suburb}.`)
      }
      else {
        // For other messages, just send a text response
        await sendEvent('assistant_text', `I received your message: "${message}". For weather information, try asking about the weather in a city. For inspection ratings, ask about ratings in a specific suburb.`)
      }
      
      // In a production application, the actual communication with ElevenLabs API
      // would happen here, and we would stream the responses as they come in
      
    } catch (error) {
      console.error('Error streaming response:', error)
      await sendEvent('error', 'An error occurred while processing your request')
    } finally {
      writer.close()
    }
  }

  // Start streaming asynchronously
  streamElevenLabsResponse()

  // Return the response stream
  return new Response(responseStream.readable, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  })
}

// In a real implementation, this would involve:
// 1. Sending audio to ElevenLabs Speech-to-Text
// 2. Processing the transcript with the ElevenLabs Agent
// 3. Handling tool calls or text responses from the Agent
// 4. Optional saving to Neon database for conversation history

export const dynamic = 'force-dynamic'
