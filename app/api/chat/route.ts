import { NextRequest } from "next/server";
import { streamText } from "ai";
import { openai } from "@ai-sdk/openai";

export const runtime = "edge";

export async function POST(req: NextRequest) {
  const { messages } = await req.json();
  if (!Array.isArray(messages)) {
    return new Response("Invalid request body", { status: 400 });
  }

  const result = streamText({
    model: openai("gpt-4o-mini"),
    system: `You are a helpful voice-assistant. If the user needs to fill a form, call the tool showDynamicForm.`,
    messages,
    tools: {
      showDynamicForm: {
        parameters: {
          type: "object",
          properties: {
            title: { type: "string" },
            fields: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  label: { type: "string" },
                  type: {
                    type: "string",
                    enum: ["text", "email", "select", "checkbox", "textarea"],
                  },
                  options: {
                    type: "array",
                    items: { type: "string" },
                  },
                  required: { type: "boolean" },
                },
                required: ["id", "label", "type"],
              },
            },
          },
          required: ["title", "fields"],
        },
        execute: async (args: any) => args,
      },
    },
  });

  return result.toDataStreamResponse();
}
