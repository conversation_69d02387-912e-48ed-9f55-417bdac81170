import { NextResponse } from "next/server"
import { AccessToken } from "livekit-server-sdk"

export async function GET() {
  const apiKey = process.env.LIVEKIT_API_KEY as string
  const apiSecret = process.env.LIVEKIT_API_SECRET as string
  const url = process.env.LIVEKIT_URL as string

  if (!apiKey || !apiSecret || !url) {
    return NextResponse.json({ error: "LiveKit env vars missing" }, { status: 500 })
  }

  const room = "dev"
  const identity = crypto.randomUUID()

  const at = new AccessToken(apiKey, apiSecret, { identity })
  at.addGrant({ room })

  return NextResponse.json({ url, token: at.toJwt(), room })
} 