import { NextRequest } from "next/server"
import OpenAI from "openai"
import { OpenAIStream, StreamingTextResponse } from "ai"

// Lazy-initialise OpenAI client using environment variable OPENAI_API_KEY.
// The Vercel AI SDK automatically reads from process.env by default, but we
// keep an explicit constructor for clarity.
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY })

export const runtime = "edge" // optional: enables the Edge Runtime on Vercel

/**
 * POST /api/ai-generate
 * body: { messages: Array<{ role: 'user' | 'assistant' | 'system', content: string }> }
 *
 * Streams chat completion tokens back to the client using the Vercel AI SDK
 * helpers (`OpenAIStream`, `StreamingTextResponse`).
 */
export async function POST(req: NextRequest) {
  const { messages } = await req.json()

  if (!Array.isArray(messages)) {
    return new Response("Invalid body; expected { messages: [] }", { status: 400 })
  }

  // Create OpenAI Chat Completion with streaming turned on
  const response = await openai.chat.completions.create({
    model: "gpt-4o-mini", // or any model your key has access to
    stream: true,
    messages,
  })

  // Convert the OpenAI response into a friendly ReadableStream
  const stream = OpenAIStream(response as any)

  // Return a StreamingTextResponse from the Vercel AI SDK — this sets correct
  // headers for chunked transfer and allows clients to consume the stream via
  // `fetch` or via the `useChat` React hook from the SDK.
  return new StreamingTextResponse(stream)
}
