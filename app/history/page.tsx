"use client"

export const dynamic = "force-dynamic"

import { useState, useEffect, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Toaster, toast } from "sonner"
import { Clock, MessageSquare, Trash2, Database } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import ErrorBoundary from "@/components/ErrorBoundary"
import { isDatabaseAvailable } from "@/lib/elevenlabs"

interface Session {
  session_id: string
  last_activity: number
  message_count: number
}

function HistoryContent() {
  const [sessions, setSessions] = useState<Session[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [usingInMemoryDb, setUsingInMemoryDb] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  const isEmbedded = searchParams.get("embed") === "1"

  // Check database availability
  useEffect(() => {
    const checkDatabase = async () => {
      try {
        const isAvailable = await isDatabaseAvailable()
        if (!isAvailable) {
          console.warn("Database is not available, using in-memory storage")
          setUsingInMemoryDb(true)
          setError("DATABASE_URL environment variable not found or database connection failed")
        }
      } catch (error) {
        console.error("Error checking database availability:", error)
        setUsingInMemoryDb(true)
        setError("Error checking database availability")
      }
    }

    checkDatabase()
  }, [])

  // Fetch all sessions
  const fetchSessions = async () => {
    try {
      setIsLoading(true)

      if (usingInMemoryDb) {
        // If using in-memory storage, we don't have a way to list all sessions
        // So we'll just show a message to the user
        setSessions([])
        setError("Database connection not available. Session history is limited to the current browser session.")
        return
      }

      const response = await fetch("/api/sessions")

      if (!response.ok) {
        throw new Error(`Failed to fetch sessions: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()

      // Log the response data for debugging
      console.log("Sessions API response:", data)

      // Ensure sessions is always an array
      if (data && Array.isArray(data.sessions)) {
        setSessions(data.sessions)
      } else {
        console.warn("Sessions data is not in expected format:", data)
        setSessions([])

        // Show error if there's one in the response
        if (data && data.error) {
          setError(data.error)
        }
      }

      // Show error message if provided in the response
      if (data && data.error) {
        setError(data.error)
      }
    } catch (error) {
      console.error("Error fetching sessions:", error)
      setError("Failed to load conversation history")
      toast.error("Failed to load conversation history")
    } finally {
      setIsLoading(false)
    }
  }

  // Delete a session
  const deleteSession = async (sessionId: string) => {
    try {
      if (usingInMemoryDb) {
        toast.error("Cannot delete sessions in memory-only mode")
        return
      }

      const response = await fetch(`/api/c?sessionId=${sessionId}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        throw new Error(`Failed to delete session: ${response.status} ${response.statusText}`)
      }

      toast.success("Conversation deleted")
      fetchSessions() // Refresh the list
    } catch (error) {
      console.error("Error deleting session:", error)
      toast.error("Failed to delete conversation")
    }
  }

  // Format timestamp
  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp * 1000)
    return date.toLocaleString()
  }

  useEffect(() => {
    if (!usingInMemoryDb) {
      fetchSessions()
    } else {
      setIsLoading(false)
    }
  }, [usingInMemoryDb])

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-zinc-900 p-6">
        <Toaster position="top-center" />

        <div className="max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold text-white">Conversation History</h1>
            {!isEmbedded && (
              <Button onClick={() => router.push("/")} className="bg-blue-600 hover:bg-blue-700">
                New Conversation
              </Button>
            )}
          </div>

          {error && (
            <div className="bg-yellow-600/20 border border-yellow-600 text-yellow-200 p-4 rounded-lg mb-6 flex items-center">
              <Database className="w-5 h-5 mr-2 flex-shrink-0" />
              <span>{error}</span>
            </div>
          )}

          {isLoading ? (
            <div className="flex justify-center py-12">
              <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            </div>
          ) : sessions.length === 0 ? (
            <div className="bg-zinc-800/50 rounded-xl p-8 text-center">
              <MessageSquare className="w-12 h-12 text-zinc-600 mx-auto mb-4" />
              <h2 className="text-xl font-medium text-zinc-300 mb-2">No conversations yet</h2>
              <p className="text-zinc-400 mb-6">Start a new conversation to see it here</p>
              {!isEmbedded ? (
                <Button onClick={() => router.push("/")} className="bg-blue-600 hover:bg-blue-700">
                  Start Conversation
                </Button>
              ) : (
                <Button
                  onClick={() => {
                    if (typeof window !== "undefined") {
                      window.parent.postMessage({ type: "startConversation" }, "*")
                    }
                  }}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Start Conversation
                </Button>
              )}
            </div>
          ) : (
            <div className="grid gap-4">
              {sessions.map((session) => (
                <div
                  key={session.session_id}
                  className="bg-zinc-800/70 rounded-xl p-5 border border-zinc-700/50 hover:border-zinc-600/50 transition-all"
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-medium text-zinc-200 mb-1 truncate max-w-md">
                        Conversation {session.session_id.substring(0, 8)}
                      </h3>
                      <div className="flex items-center text-zinc-400 text-sm mb-3">
                        <Clock className="w-4 h-4 mr-1" />
                        <span>{formatTimestamp(session.last_activity)}</span>
                        <span className="mx-2">•</span>
                        <MessageSquare className="w-4 h-4 mr-1" />
                        <span>{session.message_count} messages</span>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          if (isEmbedded && typeof window !== "undefined") {
                            window.parent.postMessage({ type: "openSession", sessionId: session.session_id }, "*")
                          } else {
                            router.push(`/c/${session.session_id}`)
                          }
                        }}
                        className="border-zinc-700 hover:border-zinc-600 text-zinc-300"
                      >
                        View
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => deleteSession(session.session_id)}
                        className="border-red-900/50 hover:border-red-700/50 text-red-400 hover:text-red-300"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </ErrorBoundary>
  )
}

export default function HistoryPage() {
  return (
    <Suspense fallback={<div className="flex justify-center py-12 text-white">Loading...</div>}>
      <HistoryContent />
    </Suspense>
  )
}
