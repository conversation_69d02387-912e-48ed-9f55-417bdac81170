'use client';
import { useChat } from '@ai-sdk/react';
import CardRenderer from '@/components/CardRenderer';

export default function ChatPage() {
  const { messages, input, setInput, handleSubmit } = useChat({ api: '/api/chat' });

  return (
    <main className="p-4 max-w-2xl mx-auto space-y-4">
      {messages.map((m) => (
        <div key={m.id} className="my-2">
          {/* textual content */}
          {m.content && <p>{m.content}</p>}

          {/* Gen-UI tool results */}
          {m.toolInvocations?.map((inv) => {
            if (inv.state !== 'result') return null;
            if (inv.toolName === 'showDynamicForm') {
              return (
                <CardRenderer
                  key={inv.toolCallId}
                  type="DynamicFormCard"
                  payload={{
                    title: inv.result.title,
                    fields: inv.result.fields,
                  }}
                />
              );
            }
            return null;
          })}
        </div>
      ))}

      <form onSubmit={handleSubmit} className="flex gap-2">
        <input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          className="flex-1 border p-2 rounded"
        />
        <button className="px-4 py-2 bg-black text-white rounded">Send</button>
      </form>
    </main>
  );
}
