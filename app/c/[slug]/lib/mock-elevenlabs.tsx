export type Role = "ai" | "user"

// This is a mock implementation for the ElevenLabs React SDK
export function useConversation(options: {
  onError?: (error: string) => void
  onConnect?: () => void
  onMessage?: (props: { message: string; source: Role }) => void
}) {
  // In a real implementation, this would connect to ElevenLabs
  const startSession = async ({ signedUrl }: { signedUrl: string }) => {
    try {
      if (!signedUrl || typeof signedUrl !== "string") {
        throw new Error("Invalid signed URL provided")
      }

      // Validate the URL format - but be more lenient
      try {
        new URL(signedUrl)
      } catch (urlError) {
        console.warn("URL validation failed, but continuing:", urlError)
        // Continue anyway to allow the app to function
      }

      // Call onConnect callback
      if (options.onConnect) {
        options.onConnect()
      }

      // Simulate a message after connection
      setTimeout(() => {
        if (options.onMessage) {
          options.onMessage({
            message: "Hello! I'm Ask <PERSON><PERSON>, your voice assistant. How can I help you today?",
            source: "ai",
          })
        }
      }, 1000)
    } catch (error) {
      console.error("Error starting session:", error)
      if (options.onError) {
        options.onError(error instanceof Error ? error.message : String(error))
      }
    }
  }

  const endSession = async () => {
    // In a real implementation, this would disconnect from ElevenLabs
    console.log("Session ended")
    return Promise.resolve()
  }

  return {
    startSession,
    endSession,
    status: "disconnected",
    isSpeaking: false,
  }
}
