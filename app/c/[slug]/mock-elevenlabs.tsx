// This is a mock implementation for development and testing

export type Role = "ai" | "user"

export function useConversation(options: {
  onError?: (error: string) => void
  onConnect?: () => void
  onMessage?: (props: { message: string; source: Role }) => void
}) {
  let status: "disconnected" | "connected" = "disconnected"
  let isSpeaking = false

  const startSession = async ({ signedUrl }: { signedUrl: string }) => {
    try {
      console.log("Mock: Starting session with signed URL:", signedUrl)

      // Set status to connected
      status = "connected"

      // Call onConnect callback
      if (options.onConnect) {
        options.onConnect()
      }

      // Simulate a message after connection
      setTimeout(() => {
        if (options.onMessage) {
          options.onMessage({
            message: "Hello! I'm Ask <PERSON>, your voice assistant. How can I help you today? (Mock Mode)",
            source: "ai",
          })
        }
      }, 1000)

      // Begin simulation of conversation after initial greeting
      simulateUserSpeaking()
    } catch (error) {
      console.error("Mock: Error starting session:", error)
      if (options.onError) {
        options.onError(error instanceof Error ? error.message : String(error))
      }
    }
  }

  const endSession = async () => {
    console.log("Mock: Ending session")
    status = "disconnected"
    return Promise.resolve()
  }

  // Simulate user speaking
  const simulateUserSpeaking = () => {
    setTimeout(() => {
      if (options.onMessage) {
        options.onMessage({
          message: "Can you tell me about the latest inspections?",
          source: "user",
        })
      }

      // Then simulate AI response
      setTimeout(() => {
        isSpeaking = true

        if (options.onMessage) {
          options.onMessage({
            message:
              "Based on the data, the latest inspection was conducted at the Booragoon location on February 28th, 2024. It received a rating of 97.11%.",
            source: "ai",
          })
        }

        // Simulate speaking finished
        setTimeout(() => {
          isSpeaking = false
        }, 3000)
      }, 1500)
    }, 5000)
  }

  // Note: simulateUserSpeaking is triggered automatically after connection

  return {
    startSession,
    endSession,
    get status() {
      return status
    },
    get isSpeaking() {
      return isSpeaking
    },
  }
}
