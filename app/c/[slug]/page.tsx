"use client"

import { toast, Toaster } from "sonner"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useCallback, useEffect, useState, useRef } from "react"
import AiTalkingAnimation from "@/components/TextAnimation"
import ErrorBoundary from "@/components/ErrorBoundary"
import { v4 as uuidv4 } from "uuid"
import { Database } from "lucide-react"
import { saveMessage, getSessionMessages, isDatabaseAvailable } from "@/lib/elevenlabs"
import Message from "@/components/Message"
import { useConversation } from "@11labs/react"
import EmailDraftCard from "@/components/EmailDraftCard"
import EnhancedEmailDraft from "@/components/EnhancedEmailDraft"
import ToolExecution from "@/components/ToolExecution"

// Define Role type for clarity
type Role = "ai" | "user"

// Message type definition
interface ConversationMessage {
  id: string
  role: string
  formatted: {
    text?: string
    transcript: string
  }
  created_at?: number
}

export default function ConversationPage() {
  const { slug } = useParams()
  const router = useRouter()
  const [currentText, setCurrentText] = useState("")
  const [messages, setMessages] = useState<ConversationMessage[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [dbError, setDbError] = useState<string | null>(null)
  // Transcript feature removed – replaced by unified conversation history view
  const [usingInMemoryDb, setUsingInMemoryDb] = useState(false)
  const [isHistoryOpen, setIsHistoryOpen] = useState(false)
  const [emailDraft, setEmailDraft] = useState<{ 
    to: string; 
    cc?: string;
    bcc?: string;
    subject: string; 
    body: string;
    priority?: 'low' | 'normal' | 'high';
    scheduledTime?: Date;
    attachments?: File[];
    signature?: string;
  } | null>(null)
  const emailDraftRef = useRef(emailDraft)
  useEffect(() => { emailDraftRef.current = emailDraft }, [emailDraft])
  
  // Tool execution state
  const [toolExecutions, setToolExecutions] = useState<Array<{
    id: string
    name: string
    status: 'executing' | 'complete' | 'error'
    args?: any
    result?: any
    error?: string
    timestamp: Date
  }>>([])

  // Always use full-screen PWA view
  // Type cast to any to allow clientTools until upstream types include it
  const conversation = useConversation({
    onError: (error: string) => {
      console.error("ElevenLabs error:", error)
      toast.error(error)
    },
    onConnect: () => {
      toast.success("Connected to voice assistant")
      setIsLoading(false)
    },
    onMessage: ({ message, source }: { message: string; source: string }) => {
      handleNewMessage(message, source as Role)
    },
    // @ts-ignore - clientTools is ElevenLabs experimental feature not yet in types
    clientTools: {
      // Email tools
      openEmailDraft: async () => {
        const execId = uuidv4()
        setToolExecutions(prev => [...prev, { id: execId, name: 'openEmailDraft', status: 'executing', timestamp: new Date() }])
        setEmailDraft({ 
          to: "", 
          cc: "",
          bcc: "",
          subject: "", 
          body: "",
          priority: 'normal',
          attachments: [],
          signature: "\n\nBest regards,\n[Your name]"
        })
        setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'complete', result: 'Email draft opened' } : t))
        return "Email draft opened"
      },
      fillEmailField: async ({ field, value }: { field: string; value: any }) => {
        const execId = uuidv4()
        setToolExecutions(prev => [...prev, { id: execId, name: 'fillEmailField', status: 'executing', args: { field, value }, timestamp: new Date() }])
        setEmailDraft(prev => prev ? { ...prev, [field]: value } : prev)
        setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'complete', result: `${field} updated` } : t))
        return `${field} updated`
      },
      
      // Additional email tools
      setEmailPriority: async ({ priority }: { priority: 'low' | 'normal' | 'high' }) => {
        const execId = uuidv4()
        setToolExecutions(prev => [...prev, { id: execId, name: 'setEmailPriority', status: 'executing', args: { priority }, timestamp: new Date() }])
        setEmailDraft(prev => prev ? { ...prev, priority } : prev)
        setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'complete', result: `Email priority set to ${priority}` } : t))
        return `Email priority set to ${priority}`
      },
      
      addEmailAttachment: async ({ fileName, fileContent }: { fileName: string; fileContent: string }) => {
        const execId = uuidv4()
        setToolExecutions(prev => [...prev, { id: execId, name: 'addEmailAttachment', status: 'executing', args: { fileName }, timestamp: new Date() }])
        try {
          // In a real implementation, you would handle file uploads properly
          // For now, we'll create a mock File object
          const file = new File([fileContent], fileName, { type: 'text/plain' })
          setEmailDraft(prev => prev ? { 
            ...prev, 
            attachments: [...(prev.attachments || []), file] 
          } : prev)
          setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'complete', result: `Attachment ${fileName} added` } : t))
          return `Attachment ${fileName} added`
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : 'Failed to add attachment'
          setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'error', error: errorMsg } : t))
          throw error
        }
      },
      sendEmail: async () => {
        const execId = uuidv4()
        setToolExecutions(prev => [...prev, { id: execId, name: 'sendEmail', status: 'executing', timestamp: new Date() }])
        try {
          const draft = emailDraftRef.current
          if (!draft || !draft.to || !draft.subject || !draft.body) {
            throw new Error("Email draft incomplete")
          }
          const res = await fetch("/api/send-email", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(draft),
          })
          const result = await res.json()
          if (!res.ok || !result.success) {
            throw new Error(result.error || "Failed to send email")
          }
          setEmailDraft(null)
          setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'complete', result: 'Email sent successfully' } : t))
          return "Email sent successfully"
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : 'Unknown error'
          setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'error', error: errorMsg } : t))
          throw error
        }
      },
      
      // Browser tools
      openUrl: async ({ url }: { url: string }) => {
        const execId = uuidv4()
        setToolExecutions(prev => [...prev, { id: execId, name: 'openUrl', status: 'executing', args: { url }, timestamp: new Date() }])
        try {
          window.open(url, '_blank')
          setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'complete', result: `Opened ${url}` } : t))
          return `Opened ${url} in new tab`
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : 'Failed to open URL'
          setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'error', error: errorMsg } : t))
          throw error
        }
      },
      
      // System tools
      showNotification: async ({ title, message }: { title: string; message: string }) => {
        const execId = uuidv4()
        setToolExecutions(prev => [...prev, { id: execId, name: 'showNotification', status: 'executing', args: { title, message }, timestamp: new Date() }])
        try {
          if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(title, { body: message })
            setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'complete', result: 'Notification shown' } : t))
            return "Notification shown"
          } else if ('Notification' in window && Notification.permission !== 'denied') {
            const permission = await Notification.requestPermission()
            if (permission === 'granted') {
              new Notification(title, { body: message })
              setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'complete', result: 'Notification shown' } : t))
              return "Notification shown"
            }
          }
          toast(message, { description: title })
          setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'complete', result: 'Toast notification shown' } : t))
          return "Toast notification shown"
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : 'Failed to show notification'
          setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'error', error: errorMsg } : t))
          throw error
        }
      },
      
      // Data tools
      copyToClipboard: async ({ text }: { text: string }) => {
        const execId = uuidv4()
        setToolExecutions(prev => [...prev, { id: execId, name: 'copyToClipboard', status: 'executing', args: { text }, timestamp: new Date() }])
        try {
          await navigator.clipboard.writeText(text)
          setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'complete', result: 'Text copied to clipboard' } : t))
          toast.success('Copied to clipboard')
          return "Text copied to clipboard"
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : 'Failed to copy to clipboard'
          setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'error', error: errorMsg } : t))
          throw error
        }
      },
      
      // MCP Bridge - Invoke MCP tools through API
      invokeMcpTool: async ({ tool, args }: { tool: string; args: any }) => {
        const execId = uuidv4()
        setToolExecutions(prev => [...prev, { id: execId, name: `mcp:${tool}`, status: 'executing', args, timestamp: new Date() }])
        try {
          const res = await fetch('/api/mcp-bridge', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ tool, args })
          })
          const result = await res.json()
          if (!res.ok) {
            throw new Error(result.error || `MCP tool ${tool} failed`)
          }
          setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'complete', result: result.data } : t))
          return result.data
        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : `Failed to invoke MCP tool ${tool}`
          setToolExecutions(prev => prev.map(t => t.id === execId ? { ...t, status: 'error', error: errorMsg } : t))
          throw error
        }
      }
    }
  })

  // Check database availability
  useEffect(() => {
    const checkDatabase = async () => {
      try {
        const isAvailable = await isDatabaseAvailable()
        if (!isAvailable) {
          console.warn("Database is not available, using in-memory storage")
          setUsingInMemoryDb(true)
          setDbError("DATABASE_URL environment variable not found or database connection failed")
        }
      } catch (error) {
        console.error("Error checking database availability:", error)
        setUsingInMemoryDb(true)
        setDbError("Error checking database availability")
      }
    }

    checkDatabase()
  }, [])

  // Handle messages from embedded iframe (history view)
  useEffect(() => {
    const messageHandler = (event: MessageEvent) => {
      if (!event?.data || typeof event.data !== "object") return

      if (event.data.type === "startConversation") {
        setIsHistoryOpen(false)
        router.push("/")
      }

      if (event.data.type === "openSession" && event.data.sessionId) {
        setIsHistoryOpen(false)
        router.push(`/c/${event.data.sessionId}`)
      }
    }

    window.addEventListener("message", messageHandler)
    return () => {
      window.removeEventListener("message", messageHandler)
    }
  }, [])

  // --------------------------------------
  // 1. Helper to append & persist messages
  // --------------------------------------
  const handleNewMessage = useCallback(async (message: string, source: Role) => {
    if (source === "ai") {
      setCurrentText(message)
    }

    // Use a client-side timestamp for new messages
    // This won't cause hydration issues since this component is already client-side
    // and messages are generated after the initial render
    const newMessage = {
      id: `item_${uuidv4()}`,
      role: source === "ai" ? "assistant" : "user",
      formatted: { transcript: message },
      created_at: Math.floor(Date.now() / 1000),
    }

    setMessages((prev) => [...prev, newMessage])

    try {
      if (slug) {
        const result = await saveMessage(slug as string, {
          id: newMessage.id,
          role: newMessage.role as any,
          content: [{ type: "text", transcript: message }],
          status: "completed",
          object: "realtime.item",
          type: "message",
        })

        if (result.inMemory) {
          // Switch the UI into fallback state silently (no alert)
          setUsingInMemoryDb(true)
        }
      }
    } catch (error) {
      console.error("Error saving message:", error)
      setDbError("Failed to save message.")
      setUsingInMemoryDb(true)
    }
  }, [slug])

  // Load conversation history
  const loadConversation = useCallback(async () => {
    if (!slug) return

    try {
      setIsLoading(true)

      try {
        const loadedMessages = await getSessionMessages(slug as string)
        setMessages(loadedMessages)

        // Set the current text to the last assistant message
        const lastAssistantMessage = loadedMessages.filter((msg: any) => msg.role === "assistant").pop()

        if (lastAssistantMessage) {
          setCurrentText(lastAssistantMessage.formatted.transcript)
        }
      } catch (error) {
        console.error("Error loading conversation:", error)
        setDbError("Failed to load conversation history.")
        setUsingInMemoryDb(true)
        toast.error("Failed to load conversation history")
      }
    } finally {
      setIsLoading(false)
    }
  }, [slug])

  // ---------------------------------------------------------------------------
  //  Connect & Disconnect helpers using the conversation instance created above
  // ---------------------------------------------------------------------------
  const connectToElevenLabs = useCallback(async () => {
    if (conversation.status === "connected") return

    setIsLoading(true)
    toast("Connecting to voice assistant…")

    try {
      // Ensure this browser supports the MediaDevices API (secure context or localhost)
      if (
        typeof navigator === "undefined" ||
        !navigator.mediaDevices ||
        typeof navigator.mediaDevices.getUserMedia !== "function"
      ) {
        toast.error("Your browser does not support microphone access")
        setIsLoading(false)
        return
      }

      await navigator.mediaDevices.getUserMedia({ audio: true })

      const res = await fetch("/api/i", { method: "POST" })
      const data = await res.json()

      if (data?.error) throw new Error(data.error)

      // Log the data returned from the API
      console.log("API response data:", data)

      // The ElevenLabs API returns a signed URL that should be passed as signedUrl
      await conversation.startSession({ signedUrl: data.apiKey })
    } catch (err: any) {
      console.error("startSession error", err)

      // More detailed error logging
      if (err && typeof err === 'object') {
        console.error("Error details:", JSON.stringify(err, null, 2))
      }

      // Improved error message
      let errorMessage = "Failed to connect to voice assistant"
      if (err?.message) {
        errorMessage += `: ${err.message}`
      }

      toast.error(errorMessage)
      setIsLoading(false)
    }
  }, [conversation])

  const disconnectFromElevenLabs = useCallback(async () => {
    if (conversation.status !== "connected") return
    try {
      await conversation.endSession()
    } catch (err) {
      console.error("endSession error", err)
    }
  }, [conversation])

  // Load conversation on mount
  useEffect(() => {
    loadConversation()

    // Cleanup on unmount
    return () => {
      disconnectFromElevenLabs()
    }
  }, [slug]) // Only depend on slug, not the functions which could cause infinite updates

  return (
    <ErrorBoundary>
      {emailDraft && (
        <EnhancedEmailDraft
          to={emailDraft.to}
          cc={emailDraft.cc}
          bcc={emailDraft.bcc}
          subject={emailDraft.subject}
          body={emailDraft.body}
          priority={emailDraft.priority}
          scheduledTime={emailDraft.scheduledTime}
          attachments={emailDraft.attachments}
          signature={emailDraft.signature}
          onChange={(field, value) => setEmailDraft(prev => prev ? { ...prev, [field]: value } : prev)}
          onCancel={() => setEmailDraft(null)}
          onSend={async () => {
            try {
              const res = await fetch("/api/send-email", { 
                method: "POST", 
                headers: { "Content-Type": "application/json" }, 
                body: JSON.stringify({
                  to: emailDraft.to,
                  cc: emailDraft.cc,
                  bcc: emailDraft.bcc,
                  subject: emailDraft.subject,
                  body: emailDraft.body,
                  priority: emailDraft.priority
                }) 
              })
              const result = await res.json()
              if (!res.ok || !result.success) throw new Error(result.error || "Send failed")
              setEmailDraft(null)
              toast.success("Email sent successfully!")
            } catch (e) {
              console.error("Send email failed", e)
              toast.error((e as Error).message)
            }
          }}
          onSaveDraft={() => {
            // In a real app, you would save this to a drafts folder
            toast.success("Draft saved")
          }}
          onSchedule={(date) => {
            setEmailDraft(prev => prev ? { ...prev, scheduledTime: date } : prev)
            toast.success(`Email scheduled for ${date.toLocaleString()}`)
          }}
        />
      )}
      {/*
        The outer wrapper adapts its layout based on whether the iPhone
        mockup should be displayed. When `showMockup` is false the content
        stretches to fill the viewport so the UI behaves like a standalone
        PWA. Otherwise the content is centred with padding so that the
        device frame is visible.
      */}
      <div className="flex flex-col min-h-screen bg-zinc-900">
        <Toaster position="top-center" />

        {/* Database warning banner */}
        {dbError && (
          <div className="fixed top-0 left-0 right-0 bg-yellow-600 text-white p-2 text-center text-sm z-50 flex items-center justify-center">
            <Database className="w-4 h-4 mr-2" />
            <span>{dbError}</span>
          </div>
        )}

        {/* ---------- UI CONTROLS (positions depend on view mode) ---------- */}

        {/* PWA mode - no toggle needed */}



        {/* PWA View - Always use full screen mode */}
        {(
          <div className="flex flex-col w-full flex-1">
            <AiTalkingAnimation
              currentText={currentText}
              onStopListening={disconnectFromElevenLabs}
              onStartListening={connectToElevenLabs}
              isAudioPlaying={false}
              messages={messages}
              onHistoryClick={() => {
                // In full-screen PWA mode simply navigate to the dedicated
                // history route. (When mock-up mode is re-enabled we can
                // switch to the in-frame overlay implementation.)
                router.push("/history")
              }}
              floatAtBottom={true}
            />

            {/* History button lives inside AiTalkingAnimation navigation bar */}
          </div>
        )}

        {/* Tool Execution Display */}
        <ToolExecution executions={toolExecutions} />

        {/* Transcript overlay removed */}
      </div>
    </ErrorBoundary>
  )
}
