# Neon Database Setup Guide

This guide will help you set up a Neon database for your ElevenLabs voice assistant application.

## Step 1: Create a Neon Account

1. Go to [Neon](https://neon.tech/) and sign up for an account if you don't have one
2. After signing up, create a new project

## Step 2: Set Up a Database

1. In your Neon dashboard, create a new database:
   - Click on "New Project"
   - Give your project a name (e.g., "voice-assistant")
   - Select a region closest to your users
   - Click "Create Project"

2. Once the project is created, you'll see a connection string. It should look something like this:
   ```
   postgres://user:password@hostname:port/database
   ```

3. Copy this connection string - you'll need it for your environment variables

## Step 3: Set Up Environment Variables

1. Create a new file called `.env` in the root of your project (if it doesn't exist already)
2. Add your Neon database connection string:
   ```
   DATABASE_URL="postgres://user:password@hostname:port/database"
   ```
   Remember to replace this with your actual connection string from Neon

3. If you're deploying to Vercel, add this environment variable in the Vercel dashboard:
   - Go to your project settings
   - Navigate to the "Environment Variables" tab
   - Add `DATABASE_URL` with your Neon connection string

## Step 4: Initialize Database Tables

Your application includes a database setup script that creates the necessary tables. Run this script to initialize your database:

```bash
node db-setup.js
```

This script will:
- Check if the `messages` table exists
- Create the table if it doesn't exist
- Create an index for faster queries
- Test the connection by inserting and retrieving a sample message

### Resetting the schema from scratch

Need to start fresh?  Use the built-in reset script:

```bash
npm run reset-db
```

The script (`scripts/reset-db.js`) will:

1. Drop the existing `messages` table (if it exists).
2. Re-create it with the schema the current code expects.
3. Re-create the composite index used for fast look-ups.

⚠️   **This deletes all conversation history stored in the table.**  Make a
backup first if you need the data.

## Step 5: Verify Database Connection

You can test your database connection by starting your application and checking the console logs or by using the test endpoint:

```
http://localhost:3000/api/test-db
```

If the connection is successful, you should see a JSON response with `"success": true`.

## Troubleshooting

If you encounter issues with your Neon database connection:

1. **Connection Error**: Make sure your DATABASE_URL environment variable is correctly set and includes the full connection string.

2. **Certificate Error**: Neon uses SSL for connections. Make sure your connection string has the correct SSL parameters.

3. **Rate Limiting**: Neon has rate limits on the free tier. If you encounter "Too Many Requests" errors, wait a few minutes and try again.

4. **Table Not Created**: If the messages table isn't created properly, you might see errors when trying to save messages. Run the `db-setup.js` script to ensure the table exists.

5. **Edge Runtime Issues**: Since the application uses Edge runtime for API routes, make sure your Neon project supports this. The Neon Serverless driver is compatible with Edge runtime.

## Additional Configuration

For production deployments:

1. Consider setting up database pooling to handle multiple concurrent connections
2. Set up backups for your database
3. Monitor your database usage to avoid hitting limits on the free tier

If you need more database capacity or features, consider upgrading your Neon plan.