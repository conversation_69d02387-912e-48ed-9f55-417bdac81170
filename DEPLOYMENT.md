# Vercel Deployment Guide

This guide helps you deploy the ElevenLabs voice assistant application to Vercel.

## Prerequisites

1. A Vercel account
2. NeonDB account (optional, for persistent storage)
3. ElevenLabs account with API key and agent ID

## Environment Variables

Set the following environment variables in your Vercel project settings:

```
DATABASE_URL=postgres://user:password@localhost:5432/voice_assistant
AGENT_ID=your-elevenlabs-agent-id
XI_API_KEY=your-elevenlabs-api-key
```

- `DATABASE_URL`: Connection string for your NeonDB database (optional, app falls back to in-memory storage)
- `AGENT_ID`: Your ElevenLabs agent ID
- `XI_API_KEY`: Your ElevenLabs API key

## Deployment Steps

1. **Prepare your repository**
   - Ensure the non-essential components have been moved to the `non-essential` directory
   - Make sure the `.env.example` file is updated with the required environment variables

2. **Connect to Vercel**
   - Create a new project in Vercel and connect it to your repository
   - Set the required environment variables in the Vercel project settings
   - Configure the build settings to use Node.js 18.x or later

3. **Build and Deploy**
   - Deploy the application using Vercel's default build settings
   - The application uses the `next-pwa` plugin to enable PWA functionality

4. **Post-Deployment**
   - After deployment, visit your application URL
   - Verify that the voice assistant works correctly
   - Test the PWA functionality by installing it on a mobile device

## Troubleshooting

If you encounter deployment issues:

1. Check the Vercel build logs for errors
2. Ensure all required environment variables are set
3. Verify that the `.vercelignore` file doesn't exclude essential files
4. Make sure the PWA configuration in `manifest.json` is correct

## Database Setup

If you're using NeonDB, ensure your database is set up with the required tables:

```sql
CREATE TABLE IF NOT EXISTS messages (
  created_at INTEGER, 
  id TEXT PRIMARY KEY, 
  session_id TEXT NOT NULL, 
  content_type TEXT, 
  content_transcript TEXT, 
  object TEXT, 
  role TEXT NOT NULL, 
  status TEXT, 
  type TEXT
);

CREATE INDEX IF NOT EXISTS idx_session_created_at ON messages (session_id, created_at);
```

You can run the `db-setup.js` script to initialize the database schema:

```
node db-setup.js
```