declare module "@11labs/react" {
  export interface ConversationOptions {
    onError?: (error: string) => void
    onConnect?: () => void
    onMessage?: (payload: { message: string; source: string }) => void
  }

  export interface Conversation {
    status: "connected" | "disconnected"
    isSpeaking: boolean
    startSession: (opts: { url?: string; signedUrl?: string; agentId?: string }) => Promise<void>
    endSession: () => Promise<void>
  }

  export function useConversation(options?: ConversationOptions): Conversation
} 