# Database connection (optional, falls back to in-memory storage)
DATABASE_URL="postgres://user:password@localhost:5432/voice_assistant"

# ElevenLabs API (required for voice functionality)
AGENT_ID="your-elevenlabs-agent-id"
XI_API_KEY="your-elevenlabs-api-key"

# Email configuration (required for email sending)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
SMTP_FROM="Your Name <<EMAIL>>"

# MCP Bridge Security (optional, but recommended for production)
# Generate a secure token: openssl rand -hex 32
MCP_SECURITY_TOKEN="your-secure-token-here"