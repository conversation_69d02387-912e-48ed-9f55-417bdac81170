# MCP Tools Integration Guide

This guide explains how to use the ElevenLabs MCP (Model Context Protocol) tool calling features in this voice assistant application.

## Overview

The application now supports two types of tools:
1. **Client Tools** - Execute directly in the browser (email, notifications, clipboard, etc.)
2. **MCP Tools** - Execute through the MCP bridge API (text-to-speech, voice cloning, agent management, etc.)

## Client Tools

Client tools are defined in the `useConversation` hook and execute directly in the user's browser.

### Available Client Tools

#### Email Tools
- `openEmailDraft` - Opens a new email draft window
- `fillEmailField` - Fills a field in the email draft (to, subject, body)
- `sendEmail` - Sends the current email draft

#### Browser Tools
- `openUrl` - Opens a URL in a new browser tab

#### System Tools
- `showNotification` - Shows a system notification or toast message
- `copyToClipboard` - Copies text to the system clipboard

#### MCP Bridge
- `invokeMcpTool` - Invokes an MCP tool through the bridge API

### Example Voice Commands

```
"Open an email draft"
"Set the email <NAME_EMAIL>"
"Set the subject to Meeting Tomorrow"
"Write the email body saying I'd like to schedule a meeting"
"Send the email"

"Open the website example.com"
"Copy this text to my clipboard: Important note"
"Show me a notification with the title Reminder"
```

## MCP Tools

MCP tools are executed through the `/api/mcp-bridge` endpoint and can interact with external services.

### Available MCP Tools (ElevenLabs)

#### Voice & Audio
- `text_to_speech` - Convert text to speech with various voices
- `speech_to_text` - Transcribe audio files to text
- `text_to_sound_effects` - Generate sound effects from text descriptions
- `voice_clone` - Clone a voice from audio samples
- `isolate_audio` - Isolate voice from background noise

#### Voice Management
- `search_voices` - Search available voices
- `get_voice` - Get details of a specific voice

#### Agent Management
- `create_agent` - Create a new conversational AI agent
- `list_agents` - List all available agents
- `get_agent` - Get details of a specific agent

### Example MCP Tool Usage

To use MCP tools through voice, you can say:

```
"Use the MCP tool to convert this text to speech: Hello world"
"Search for available voices"
"List all my agents"
```

## Configuration

### Environment Variables

Add these to your `.env` file:

```env
# MCP Bridge Security (optional but recommended)
MCP_SECURITY_TOKEN="your-secure-token-here"
```

### Security

The MCP bridge API uses a security token to prevent unauthorized access. In production:

1. Generate a secure token: `openssl rand -hex 32`
2. Set it in your environment variables
3. Include it in API requests as the `x-mcp-security-token` header

## Tool Execution UI

The application displays real-time tool execution status in the bottom-right corner:

- **Blue spinner** - Tool is executing
- **Green checkmark** - Tool completed successfully
- **Red X** - Tool execution failed

Click on any tool execution to see:
- Input arguments
- Execution result or error
- Timestamp

## Adding New Tools

### Adding a Client Tool

1. Add the tool definition to `/lib/elevenlabs-tools.ts`
2. Implement the tool in the `clientTools` object in `/app/c/[slug]/page.tsx`
3. Update the ElevenLabs agent configuration to recognize the tool

### Adding an MCP Tool

1. Add the tool mapping in `/app/api/mcp-bridge/route.ts`
2. Add the tool definition to `/lib/mcp-tools.ts`
3. Implement the actual MCP communication (requires additional setup)

## Limitations

Currently, the MCP bridge returns mock responses. To enable actual MCP tool execution:

1. Set up a Node.js server that can communicate with MCP servers
2. Implement WebSocket or SSE communication
3. Handle MCP protocol authentication and message passing

## Best Practices

1. **Tool Naming** - Use clear, descriptive names (e.g., `openEmailDraft` not just `email`)
2. **Error Handling** - Always wrap tool executions in try-catch blocks
3. **User Feedback** - Show clear status indicators for tool execution
4. **Security** - Never expose sensitive data in tool arguments or results
5. **Logging** - Log all tool executions for debugging and analytics

## Troubleshooting

### Tools not being recognized
- Check the ElevenLabs agent configuration
- Ensure tool names match exactly
- Verify the agent has the latest tool definitions

### MCP bridge errors
- Check the security token is set correctly
- Verify the tool name exists in the mapping
- Check server logs for detailed error messages

### Client tool failures
- Check browser console for errors
- Ensure required permissions (notifications, clipboard)
- Verify API endpoints are accessible