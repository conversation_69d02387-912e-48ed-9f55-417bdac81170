import { TrendingUp, MapPin, User, Clock, BarChart2 } from "lucide-react"

/*
  This file contains static data used by the voice-assistant demo. The file was
  renamed from .ts to .tsx so that the embedded JSX icons (<User />, <MapPin />
  etc.) are treated correctly by the TypeScript compiler.  No runtime changes
  are introduced – the export shape remains identical.
*/

const voiceAssistantQAData = [
  {
    title: "How many inspections did <PERSON> conduct at Willetton?",
    meta: "QA Inspections Data",
    description:
      "Based on the data, <PERSON> conducted inspections at Willetton, with one recorded on February twenty-eighth, two thousand twenty-four.",
    icon: <User className="w-6 h-6" />,
    status: "Active",
    tags: ["QA Inspections Data"],
  },
  {
    title: "What was the rating for the Subiaco Rokeby Road location?",
    meta: "QA Inspections Data",
    description: "The AUSPOST Subiaco Rokeby Road location received a rating of ninety-seven percent.",
    icon: <BarChart2 className="w-6 h-6" />,
    status: "Active",
    tags: ["QA Inspections Data", "QA000095"],
  },
  {
    title: "What's the address of the Willetton location?",
    meta: "QA Inspections Data",
    description: "The Willetton location is at forty-five Burrendah Boulevarde.",
    icon: <MapPin className="w-6 h-6" />,
    status: "Active",
    tags: ["QA Inspections Data", "QA000109"],
  },
  {
    title: "What's the address of the Booragoon location?",
    meta: "QA Inspections Data",
    description: "The Booragoon location is at nine slash two seventy-six Leach Highway.",
    icon: <MapPin className="w-6 h-6" />,
    status: "Active",
    tags: ["QA Inspections Data", "QA000110"],
  },
  {
    title: "What rating did the Booragoon Leach Highway location receive?",
    meta: "QA Inspections Data",
    description:
      "The AUSPOST Booragoon Leach Highway location received a rating of ninety-seven point one one percent.",
    icon: <BarChart2 className="w-6 h-6" />,
    status: "Active",
    tags: ["QA Inspections Data", "QA000110"],
  },
  {
    title: "When was the Booragoon location inspected?",
    meta: "QA Inspections Data",
    description:
      "The Booragoon location was inspected on February twenty-eighth, two thousand twenty-four, at twelve thirty-nine PM.",
    icon: <Clock className="w-6 h-6" />,
    status: "Active",
    tags: ["QA Inspections Data", "QA000110"],
  },
  {
    title: "Which cities have the most inspections?",
    meta: "Statistical Insights",
    description:
      "Joondalup and Bibra Lake lead with four inspections each, followed by Clarkson with three inspections.",
    icon: <TrendingUp className="w-6 h-6" />,
    status: "Active",
    tags: ["Statistical Insights", "City Distribution"],
  },
  {
    title: "Are there consistent naming conventions for locations?",
    meta: "Statistical Insights",
    description: "Yes, uniform location naming conventions are maintained across the database.",
    icon: <MapPin className="w-6 h-6" />,
    status: "Active",
    tags: ["Statistical Insights", "Consistency"],
  },
  {
    title: "Who is the primary inspector for the program?",
    meta: "Statistical Insights",
    description: "Julian Garlett is the primary inspector, conducting sixty-eight out of sixty-nine inspections.",
    icon: <User className="w-6 h-6" />,
    status: "Active",
    tags: ["Statistical Insights", "Inspector Coverage"],
  },
  {
    title: "What's the median inspection rating?",
    meta: "Statistical Insights",
    description: "The median inspection rating is ninety-two point zero nine percent.",
    icon: <BarChart2 className="w-6 h-6" />,
    status: "Active",
    tags: ["Statistical Insights", "Key Metrics"],
  },
]

export default voiceAssistantQAData
