{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": [".next", "dist", ".turbo", "dev-dist", ".zed", ".vscode", "routeTree.gen.ts", "src-tauri", ".nuxt"]}, "formatter": {"enabled": true, "indentStyle": "tab"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"useExhaustiveDependencies": "info"}, "nursery": {"useSortedClasses": {"level": "warn", "fix": "safe", "options": {"functions": ["clsx", "cva", "cn"]}}}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}