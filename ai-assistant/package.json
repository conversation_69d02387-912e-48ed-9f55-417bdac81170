{"name": "ai-assistant", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"check": "biome check --write .", "prepare": "husky", "dev": "turbo dev", "build": "turbo build", "check-types": "turbo check-types", "dev:native": "turbo -F native dev", "dev:web": "turbo -F web dev", "dev:server": "turbo -F @ai-assistant/backend dev", "dev:setup": "turbo -F @ai-assistant/backend setup"}, "dependencies": {}, "devDependencies": {"@biomejs/biome": "1.9.4", "husky": "^9.1.7", "lint-staged": "^15.5.2", "turbo": "^2.5.4"}, "lint-staged": {"*.{js,ts,cjs,mjs,d.cts,d.mts,jsx,tsx,json,jsonc}": ["biome check --write ."]}, "packageManager": "bun@1.2.16"}