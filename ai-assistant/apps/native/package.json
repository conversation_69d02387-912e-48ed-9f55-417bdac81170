{"name": "native", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"dev": "expo start --clear", "android": "expo run:android", "ios": "expo run:ios", "prebuild": "expo prebuild", "web": "expo start --web"}, "dependencies": {"@ai-assistant/backend": "workspace:*", "@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.3.15", "@react-navigation/drawer": "^7.4.2", "@react-navigation/native": "^7.1.11", "@tanstack/react-form": "^1.12.3", "@tanstack/react-query": "^5.80.7", "convex": "^1.24.8", "expo": "^53.0.11", "expo-constants": "~17.1.6", "expo-linking": "~7.1.5", "expo-navigation-bar": "~4.2.5", "expo-router": "~5.0.7", "expo-secure-store": "~14.2.3", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.8", "expo-web-browser": "~14.1.6", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.1", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.3.0", "react-native-screens": "~4.10.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@types/react": "~19.0.14", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "private": true}