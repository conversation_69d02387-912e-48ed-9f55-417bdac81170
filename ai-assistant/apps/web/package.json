{"name": "web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port=3001", "build": "next build", "start": "next start", "lint": "next lint", "tauri": "tauri", "desktop:dev": "tauri dev", "desktop:build": "tauri build"}, "dependencies": {"@ai-assistant/backend": "workspace:*", "@tanstack/react-form": "^1.12.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.24.8", "lucide-react": "^0.487.0", "next": "15.3.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "zod": "^3.25.67"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@tauri-apps/cli": "^2.5.0", "@types/node": "^20.19.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "tailwindcss": "^4.1.10", "typescript": "^5.8.3"}}