# Mobile Mock-up Toggle Rule

**Context**: The application can be viewed either inside an iPhone-style mock-up frame (`showMockup === true`) or in full-screen/PWA mode (`showMockup === false`).  This rule formalises how new UI elements must behave depending on the current mode so that developers—and LLM coding agents—do not introduce regressions.

---

## 1. Toggle semantics

*   `showMockup` lives in `app/c/[slug]/page.tsx`.
*   It is controlled by the top-left “Full Screen / Phone Mockup” button.

## 2. Layout requirements

| UI element                           | When **showMockup = true**                              | When **showMockup = false**             |
|--------------------------------------|---------------------------------------------------------|------------------------------------------|
| Main chat view                       | Rendered inside `<IPhoneMockup>` frame                  | Fills entire viewport                    |
| Navigation bar                       | Same component reused; no changes                       | Same component                           |
| Transcript overlay                   | Must be an **absolute child of** `<IPhoneMockup>`       | Uses `fixed` positioning (viewport)      |
| DB warning banner                    | Absolute within mock-up (top)                           | Fixed at top of viewport                 |
| Conversation history (opened via nav)| Overlay inside mock-up using `isHistoryOpen`; loads `/history?embed=1` in iframe | Route change to `/history` page |

## 3. Implementation hints

*   Always gate behaviour with `showMockup` checks at *interaction* time (e.g. onClick) rather than during initial render only.
*   Overlays inside the phone should use `absolute inset-0` to cover the frame and `z-40` or higher.
*   Avoid `fixed` positioning while in mock-up mode.

## 4. Testing checklist (PR / LLM prompts)

1. Toggle mock-up ON – verify all modals, banners, and overlays stay inside the device frame.
2. Activate assistant → History button becomes **X**; press it and ensure assistant stops.
3. Press History while mock-up ON → in-frame overlay opens.
4. Press Full-Screen → overlays convert to viewport-wide implementation.

## 5. Why this matters

Consistent adherence guarantees a seamless “native-app” experience inside the mock-up without special-casing every future UI addition.  It also prevents UI bugs where pop-ups appear outside the visible device area.

---

Please keep this file up to date whenever related behaviour changes.
