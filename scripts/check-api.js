// Script to check the API endpoints
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function checkApi() {
  try {
    console.log('Checking API endpoints...');

    // Check sessions endpoint
    console.log('\nFetching sessions from API...');
    try {
      const sessionsResponse = await fetch('http://localhost:3010/api/sessions');

      if (!sessionsResponse.ok) {
        console.error(`Error fetching sessions: ${sessionsResponse.status} ${sessionsResponse.statusText}`);
      } else {
        const sessionsData = await sessionsResponse.json();
        console.log('Sessions API response:', JSON.stringify(sessionsData, null, 2));
      }
    } catch (error) {
      console.error('Error fetching sessions:', error.message);
    }

    // Check specific conversation endpoint
    const sessionId = 'b30cd7d2-f063-4d0e-836a-efe4b9ca5d3a'; // Use the session ID from our generated conversation
    console.log(`\nFetching messages for session ${sessionId}...`);

    try {
      const messagesResponse = await fetch(`http://localhost:3010/api/c?id=${sessionId}`);

      if (!messagesResponse.ok) {
        console.error(`Error fetching messages: ${messagesResponse.status} ${messagesResponse.statusText}`);
      } else {
        const messagesData = await messagesResponse.json();
        console.log(`Found ${messagesData.length} messages for session ${sessionId}`);

        if (messagesData.length > 0) {
          console.log('First message:', JSON.stringify(messagesData[0], null, 2));
        }
      }
    } catch (error) {
      console.error('Error fetching messages:', error.message);
    }
  } catch (error) {
    console.error('Error checking API:', error);
  }
}

// Run the script
checkApi();
