// Script to fix API issues by checking the database connection
const fs = require('fs');
const { neon, neonConfig } = require('@neondatabase/serverless');

// Configure neon to use fetch for pooling
neonConfig.poolQueryViaFetch = true;
neonConfig.fetchTimeout = 10000;
neonConfig.fetchRetry = 3;
neonConfig.fetchRetryMaxDelay = 2000;

// Load environment variables
function loadEnv() {
  try {
    let envFile;
    try {
      envFile = fs.readFileSync('.env.local', 'utf-8');
    } catch (err) {
      try {
        envFile = fs.readFileSync('.env', 'utf-8');
      } catch (err2) {
        console.error('Neither .env nor .env.local file found');
        process.exit(1);
      }
    }

    const dbUrlMatch = envFile.match(/DATABASE_URL="([^"]+)"/);

    if (!dbUrlMatch) {
      console.error('DATABASE_URL not found in environment files');
      process.exit(1);
    }

    // Set environment variable for other modules that might use it
    process.env.DATABASE_URL = dbUrlMatch[1];
    return process.env.DATABASE_URL;
  } catch (error) {
    console.error('Error loading environment variables:', error);
    process.exit(1);
  }
}

// Get database connection
function getDb() {
  if (!process.env.DATABASE_URL) {
    throw new Error("DATABASE_URL environment variable not found");
  }
  return neon(process.env.DATABASE_URL);
}

// Fix API issues
async function fixApiIssues() {
  try {
    // Load environment variables
    const databaseUrl = loadEnv();
    console.log('Using database URL:', databaseUrl.substring(0, 25) + '...');

    // Get a database connection
    const sql = getDb();

    // Check if the messages table exists
    console.log('Checking if messages table exists...');
    const tableExists = await sql.query(
      "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)",
      ["messages"]
    );
    
    console.log('Table exists query result:', JSON.stringify(tableExists, null, 2));

    // Check if we can access the sessions
    console.log('\nTesting sessions query...');
    try {
      const sessionsRes = await sql.query(
        `SELECT
          session_id,
          MAX(created_at) as last_activity,
          COUNT(*) as message_count
        FROM
          messages
        GROUP BY
          session_id
        ORDER BY
          last_activity DESC`
      );
      
      console.log(`Found ${sessionsRes.length} sessions in the database.`);
      
      // Create a test session to ensure the API can access it
      const testSessionId = 'test-api-session-' + Date.now();
      console.log(`\nCreating a test session with ID: ${testSessionId}`);
      
      // Insert a test message
      await sql.query(
        "INSERT INTO messages (created_at, id, session_id, content_type, content_transcript, object, role, status, type) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)",
        [
          Math.floor(Date.now() / 1000),
          'test-msg-' + Date.now(),
          testSessionId,
          'text',
          'This is a test message for API troubleshooting',
          'realtime.item',
          'user',
          'completed',
          'message'
        ]
      );
      
      console.log('Test message inserted successfully.');
      
      // Verify the test message was inserted
      const testMessage = await sql.query(
        "SELECT * FROM messages WHERE session_id = $1",
        [testSessionId]
      );
      
      console.log(`Retrieved ${testMessage.length} test messages.`);
      
      if (testMessage.length > 0) {
        console.log('Test message details:', JSON.stringify(testMessage[0], null, 2));
      }
      
      console.log('\nAPI troubleshooting complete.');
      console.log('If the API is still not working, check:');
      console.log('1. The API routes in app/api/sessions/route.ts and app/api/c/route.ts');
      console.log('2. The database connection in lib/db.ts');
      console.log('3. The environment variables in .env and .env.local');
      console.log('4. The server logs for any errors');
      console.log('\nYou can access the test session at:');
      console.log(`http://localhost:3010/c/${testSessionId}`);
      
    } catch (error) {
      console.error('Error testing database:', error);
    }

  } catch (error) {
    console.error('Error fixing API issues:', error);
    process.exit(1);
  }
}

// Run the script
fixApiIssues();
