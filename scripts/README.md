# Scripts for ARAPS

This directory contains utility scripts for the ARAPS application.

## Conversation Generator

The `generate-conversation.js` script creates a mock conversation history and pushes it to the database. This is useful for testing the conversation history functionality.

### Prerequisites

- Node.js installed
- `.env` file with a valid `DATABASE_URL` for the Neon PostgreSQL database
- Required npm packages: `uuid` and `@neondatabase/serverless`

### Installation

Make sure you have the required packages installed:

```bash
npm install uuid @neondatabase/serverless
```

### Usage

Run the script from the project root directory:

```bash
node scripts/generate-conversation.js
```

### What it does

1. Connects to the Neon PostgreSQL database using the connection string from the `.env` file
2. Generates a unique session ID for the conversation
3. Creates a series of 10 messages between user and assistant about a property inspection
4. Inserts these messages into the database with proper timestamps
5. Verifies the insertion was successful
6. Outputs the session ID that can be used to view the conversation in the app

### Example Output

```
Using database URL: postgresql://neondb_owner...
Generating conversation with session ID: 31f4eff0-bdb9-44f4-bd5f-5a129d58df13
Inserting messages into the database...
Inserted message: user - Hello, I need help with my pro...
Inserted message: assistant - Hi there! I'd be happy to help...
...
Success! All messages were inserted correctly.

Conversation session ID: 31f4eff0-bdb9-44f4-bd5f-5a129d58df13
You can view this conversation in the app by navigating to:
http://localhost:3000/c/31f4eff0-bdb9-44f4-bd5f-5a129d58df13
```

### Viewing the Conversation

After running the script, you can view the generated conversation in the app by:

1. Starting the development server: `npm run dev`
2. Navigating to the URL provided in the script output, e.g., `http://localhost:3000/c/31f4eff0-bdb9-44f4-bd5f-5a129d58df13`

Alternatively, you can see all conversations in the history page: `http://localhost:3000/history`
