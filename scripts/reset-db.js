#!/usr/bin/env node
// Reset (drop & recreate) the `messages` table in the Neon database.

// Load env vars from .env / .env.local automatically if present.
require('dotenv/config')

const { neon, neonConfig } = require('@neondatabase/serverless')

// ---- Neon client configuration -------------------------------------------
neonConfig.poolQueryViaFetch = true
neonConfig.fetchTimeout = 10000
neonConfig.fetchRetry = 3
neonConfig.fetchRetryMaxDelay = 2000

async function main() {
  const dbUrl = process.env.DATABASE_URL

  if (!dbUrl) {
    console.error('❌  DATABASE_URL environment variable not found.')
    process.exit(1)
  }

  const sql = neon(dbUrl)

  try {
    console.log('🚧 Dropping existing `messages` table (if any)…')
    await sql`DROP TABLE IF EXISTS messages;`

    console.log('🔨 Re-creating `messages` table…')
    await sql`CREATE TABLE IF NOT EXISTS messages (
      created_at INTEGER,
      id TEXT PRIMARY KEY,
      session_id TEXT NOT NULL,
      content_type TEXT,
      content_transcript TEXT,
      object TEXT,
      role TEXT NOT NULL,
      status TEXT,
      type TEXT
    );`

    console.log('🔧 Creating index…')
    await sql`CREATE INDEX IF NOT EXISTS idx_session_created_at ON messages (session_id, created_at);`

    console.log('✅  Database schema reset complete.')
    process.exit(0)
  } catch (err) {
    console.error('💥  Failed to reset schema:', err)
    process.exit(1)
  }
}

main()
