// <PERSON>ript to check the database for conversations
const fs = require('fs');
const { neon, neonConfig } = require('@neondatabase/serverless');

// Configure neon to use fetch for pooling
neonConfig.poolQueryViaFetch = true;
neonConfig.fetchTimeout = 10000;
neonConfig.fetchRetry = 3;
neonConfig.fetchRetryMaxDelay = 2000;

// Load environment variables
function loadEnv() {
  try {
    let envFile;
    try {
      envFile = fs.readFileSync('.env.local', 'utf-8');
    } catch (err) {
      try {
        envFile = fs.readFileSync('.env', 'utf-8');
      } catch (err2) {
        console.error('Neither .env nor .env.local file found');
        process.exit(1);
      }
    }

    const dbUrlMatch = envFile.match(/DATABASE_URL="([^"]+)"/);

    if (!dbUrlMatch) {
      console.error('DATABASE_URL not found in environment files');
      process.exit(1);
    }

    // Set environment variable for other modules that might use it
    process.env.DATABASE_URL = dbUrlMatch[1];
    return process.env.DATABASE_URL;
  } catch (error) {
    console.error('Error loading environment variables:', error);
    process.exit(1);
  }
}

// Get database connection
function getDb() {
  if (!process.env.DATABASE_URL) {
    throw new Error("DATABASE_URL environment variable not found");
  }
  return neon(process.env.DATABASE_URL);
}

// Check database for conversations
async function checkDatabase() {
  try {
    // Load environment variables
    const databaseUrl = loadEnv();
    console.log('Using database URL:', databaseUrl.substring(0, 25) + '...');

    // Get a database connection
    const sql = getDb();

    // Check if the messages table exists
    console.log('Checking if messages table exists...');
    const tableExists = await sql.query(
      "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)",
      ["messages"]
    );
    
    console.log('Table exists query result:', JSON.stringify(tableExists, null, 2));

    // Get all unique session IDs
    console.log('\nFetching all sessions...');
    try {
      const sessionsRes = await sql.query(
        `SELECT
          session_id,
          MAX(created_at) as last_activity,
          COUNT(*) as message_count
        FROM
          messages
        GROUP BY
          session_id
        ORDER BY
          last_activity DESC`
      );
      
      console.log('Sessions query result:', JSON.stringify(sessionsRes, null, 2));
      
      if (Array.isArray(sessionsRes) && sessionsRes.length > 0) {
        console.log(`\nFound ${sessionsRes.length} sessions in the database.`);
        
        // Print details for each session
        for (const session of sessionsRes) {
          console.log(`\nSession ID: ${session.session_id}`);
          console.log(`Last activity: ${new Date(session.last_activity * 1000).toLocaleString()}`);
          console.log(`Message count: ${session.message_count}`);
          
          // Get messages for this session
          const messagesRes = await sql.query(
            "SELECT * FROM messages WHERE session_id = $1 ORDER BY created_at ASC LIMIT 3",
            [session.session_id]
          );
          
          console.log(`First ${Math.min(3, messagesRes.length)} messages:`);
          for (const msg of messagesRes.slice(0, 3)) {
            console.log(`- ${msg.role}: ${msg.content_transcript.substring(0, 50)}...`);
          }
        }
      } else {
        console.log('No sessions found in the database.');
      }
    } catch (error) {
      console.error('Error fetching sessions:', error);
    }

  } catch (error) {
    console.error('Error checking database:', error);
    process.exit(1);
  }
}

// Run the script
checkDatabase();
