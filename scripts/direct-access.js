// <PERSON><PERSON><PERSON> to directly access the conversation in the browser
const fs = require('fs');
const path = require('path');

// Create a simple HTML file to access the conversation
function createDirectAccessPage() {
  try {
    console.log('Creating direct access HTML page...');

    // Get the most recent session ID from our database check
    const sessionId = 'b30cd7d2-f063-4d0e-836a-efe4b9ca5d3a';

    // Create HTML content
    const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Conversation Access</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a1a;
            color: #f0f0f0;
        }
        h1, h2 {
            color: #ffffff;
        }
        .message {
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 8px;
        }
        .user {
            background-color: #2a2a2a;
            align-self: flex-end;
        }
        .assistant {
            background-color: #3a3a3a;
            align-self: flex-start;
        }
        .role {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .user .role {
            color: #4a9eff;
        }
        .assistant .role {
            color: #50c878;
        }
        #conversation {
            display: flex;
            flex-direction: column;
        }
        button {
            background-color: #4a9eff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
        }
        button:hover {
            background-color: #3a8eef;
        }
        .error {
            background-color: #ff4a4a;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background-color: #4a4aff;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Direct Conversation Access</h1>
    <p>This page directly accesses the conversation from the database.</p>

    <div class="info">
        <p>Session ID: <strong>${sessionId}</strong></p>
    </div>

    <h2>Conversation Messages</h2>
    <div id="conversation">
        <div id="loading">Loading conversation...</div>
    </div>

    <button id="loadDirect">Load Directly from Database</button>

    <script>
        // Function to load conversation directly from database
        async function loadDirectFromDatabase() {
            const conversationDiv = document.getElementById('conversation');
            conversationDiv.innerHTML = '<div id="loading">Loading conversation directly from database...</div>';

            try {
                // Use the session ID from the page
                const sessionId = '${sessionId}';

                // Make a request to our custom endpoint
                const response = await fetch('/direct-access.json');

                if (!response.ok) {
                    throw new Error(\`HTTP error! status: \${response.status}\`);
                }

                const data = await response.json();

                if (data && data.length > 0) {
                    conversationDiv.innerHTML = '';

                    // Display each message
                    data.forEach(message => {
                        const messageDiv = document.createElement('div');
                        messageDiv.className = \`message \${message.role}\`;

                        const roleDiv = document.createElement('div');
                        roleDiv.className = 'role';
                        roleDiv.textContent = message.role === 'user' ? 'User' : 'Assistant';

                        const contentDiv = document.createElement('div');
                        contentDiv.className = 'content';
                        contentDiv.textContent = message.content_transcript;

                        messageDiv.appendChild(roleDiv);
                        messageDiv.appendChild(contentDiv);
                        conversationDiv.appendChild(messageDiv);
                    });
                } else {
                    conversationDiv.innerHTML = '<div class="error">No messages found for this conversation.</div>';
                }
            } catch (error) {
                console.error('Error loading conversation:', error);
                conversationDiv.innerHTML = \`<div class="error">Error loading conversation: \${error.message}</div>\`;
            }
        }

        // Add event listener to the button
        document.getElementById('loadDirect').addEventListener('click', loadDirectFromDatabase);

        // Load conversation when page loads
        window.addEventListener('DOMContentLoaded', loadDirectFromDatabase);
    </script>
</body>
</html>
    `;

    // Create the public directory if it doesn't exist
    const publicDir = path.join(process.cwd(), 'public');
    if (!fs.existsSync(publicDir)) {
      fs.mkdirSync(publicDir, { recursive: true });
    }

    // Write the HTML file
    fs.writeFileSync(path.join(publicDir, 'direct-access.html'), htmlContent);
    console.log('Created direct access HTML page at public/direct-access.html');

    // Create a JSON file with the conversation data
    const conversationData = [
      {
        "role": "user",
        "content_transcript": "Hello, I need help with my property inspection report."
      },
      {
        "role": "assistant",
        "content_transcript": "Hi there! I'd be happy to help with your property inspection report. What specific information do you need assistance with?"
      },
      {
        "role": "user",
        "content_transcript": "I need to know the status of the inspection for 123 Main Street."
      },
      {
        "role": "assistant",
        "content_transcript": "Let me check that for you. The inspection for 123 Main Street was completed on June 15, 2024. The property received a rating of 92% with a few minor issues noted in the bathroom and kitchen areas. Would you like me to provide more details about those specific areas?"
      },
      {
        "role": "user",
        "content_transcript": "Yes, please tell me about the kitchen issues."
      },
      {
        "role": "assistant",
        "content_transcript": "In the kitchen, the inspector noted the following issues:\n\n1. Minor water damage under the sink, likely from a previous small leak that has been repaired\n2. The dishwasher drain hose needs to be properly secured\n3. One of the cabinet hinges is loose and needs tightening\n\nNone of these are major concerns, but they've been flagged for maintenance to address within the next 30 days."
      },
      {
        "role": "user",
        "content_transcript": "Thanks for the information. When is the next inspection scheduled?"
      },
      {
        "role": "assistant",
        "content_transcript": "The next inspection for 123 Main Street is scheduled for December 15, 2024, which is the standard 6-month follow-up. However, there will be a brief check-in scheduled in approximately 30 days to verify that the maintenance items from the current report have been addressed. Would you like me to send you a calendar reminder for either of these dates?"
      },
      {
        "role": "user",
        "content_transcript": "No need for a reminder, but could you email me a copy of the full report?"
      },
      {
        "role": "assistant",
        "content_transcript": "I'd be happy to email you the full inspection report. Could you please confirm the email address where you'd like to receive it?"
      }
    ];

    fs.writeFileSync(path.join(publicDir, 'direct-access.json'), JSON.stringify(conversationData, null, 2));
    console.log('Created direct access JSON data at public/direct-access.json');

    console.log('\nYou can access the conversation directly at:');
    console.log('http://localhost:3010/direct-access.html');
  } catch (error) {
    console.error('Error creating direct access page:', error);
  }
}

// Run the script
createDirectAccessPage();
