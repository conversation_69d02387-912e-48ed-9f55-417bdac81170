// <PERSON>ript to generate a conversation history and push it to the database
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const { neon, neonConfig } = require('@neondatabase/serverless');

// Configure neon to use fetch for pooling
neonConfig.poolQueryViaFetch = true;
neonConfig.fetchTimeout = 10000;
neonConfig.fetchRetry = 3;
neonConfig.fetchRetryMaxDelay = 2000;

// Load environment variables
function loadEnv() {
  try {
    let envFile;
    try {
      envFile = fs.readFileSync('.env.local', 'utf-8');
    } catch (err) {
      try {
        envFile = fs.readFileSync('.env', 'utf-8');
      } catch (err2) {
        console.error('Neither .env nor .env.local file found');
        process.exit(1);
      }
    }

    const dbUrlMatch = envFile.match(/DATABASE_URL="([^"]+)"/);

    if (!dbUrlMatch) {
      console.error('DATABASE_URL not found in environment files');
      process.exit(1);
    }

    // Set environment variable for other modules that might use it
    process.env.DATABASE_URL = dbUrlMatch[1];
    return process.env.DATABASE_URL;
  } catch (error) {
    console.error('Error loading environment variables:', error);
    process.exit(1);
  }
}

// Get database connection
function getDb() {
  if (!process.env.DATABASE_URL) {
    throw new Error("DATABASE_URL environment variable not found");
  }
  return neon(process.env.DATABASE_URL);
}

// Create a new message
async function createMessage(message) {
  const sql = getDb();

  try {
    await sql.query(
      "INSERT INTO messages (created_at, id, session_id, content_type, content_transcript, object, role, status, type) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) ON CONFLICT (id) DO UPDATE SET content_transcript = $5, status = $8",
      [
        message.created_at,
        message.id,
        message.session_id,
        message.content_type || "text",
        message.content_transcript || "",
        message.object || "realtime.item",
        message.role,
        message.status || "completed",
        message.type || "message",
      ]
    );
    return { success: true, id: message.id };
  } catch (error) {
    console.error("Error creating message:", error);
    throw error;
  }
}

// Get messages for a session
async function getSessionMessages(sessionId) {
  const sql = getDb();

  try {
    const messagesRes = await sql.query(
      "SELECT * FROM messages WHERE session_id = $1 ORDER BY created_at ASC",
      [sessionId]
    );

    // Ensure we have a valid response with rows property
    if (messagesRes && messagesRes.rows) {
      return messagesRes.rows;
    } else {
      console.warn("Warning: Unexpected response format from database query");
      return [];
    }
  } catch (error) {
    console.error("Error getting session messages:", error);
    throw error;
  }
}

// Generate a mock conversation
async function generateConversation() {
  try {
    // Load environment variables
    const databaseUrl = loadEnv();
    console.log('Using database URL:', databaseUrl.substring(0, 25) + '...');

    // Get a database connection
    const sql = getDb();

    // Generate a unique session ID
    const sessionId = uuidv4();
    console.log(`Generating conversation with session ID: ${sessionId}`);

    // Create timestamp base (starting from 10 minutes ago)
    const baseTimestamp = Math.floor(Date.now() / 1000) - 600;

    // Define conversation messages
    const conversation = [
      {
        id: `msg_${uuidv4()}`,
        role: "user",
        content_transcript: "Hello, I need help with my property inspection report.",
        created_at: baseTimestamp
      },
      {
        id: `msg_${uuidv4()}`,
        role: "assistant",
        content_transcript: "Hi there! I'd be happy to help with your property inspection report. What specific information do you need assistance with?",
        created_at: baseTimestamp + 5
      },
      {
        id: `msg_${uuidv4()}`,
        role: "user",
        content_transcript: "I need to know the status of the inspection for 123 Main Street.",
        created_at: baseTimestamp + 30
      },
      {
        id: `msg_${uuidv4()}`,
        role: "assistant",
        content_transcript: "Let me check that for you. The inspection for 123 Main Street was completed on June 15, 2024. The property received a rating of 92% with a few minor issues noted in the bathroom and kitchen areas. Would you like me to provide more details about those specific areas?",
        created_at: baseTimestamp + 35
      },
      {
        id: `msg_${uuidv4()}`,
        role: "user",
        content_transcript: "Yes, please tell me about the kitchen issues.",
        created_at: baseTimestamp + 60
      },
      {
        id: `msg_${uuidv4()}`,
        role: "assistant",
        content_transcript: "In the kitchen, the inspector noted the following issues:\n\n1. Minor water damage under the sink, likely from a previous small leak that has been repaired\n2. The dishwasher drain hose needs to be properly secured\n3. One of the cabinet hinges is loose and needs tightening\n\nNone of these are major concerns, but they've been flagged for maintenance to address within the next 30 days.",
        created_at: baseTimestamp + 65
      },
      {
        id: `msg_${uuidv4()}`,
        role: "user",
        content_transcript: "Thanks for the information. When is the next inspection scheduled?",
        created_at: baseTimestamp + 90
      },
      {
        id: `msg_${uuidv4()}`,
        role: "assistant",
        content_transcript: "The next inspection for 123 Main Street is scheduled for December 15, 2024, which is the standard 6-month follow-up. However, there will be a brief check-in scheduled in approximately 30 days to verify that the maintenance items from the current report have been addressed. Would you like me to send you a calendar reminder for either of these dates?",
        created_at: baseTimestamp + 95
      },
      {
        id: `msg_${uuidv4()}`,
        role: "user",
        content_transcript: "No need for a reminder, but could you email me a copy of the full report?",
        created_at: baseTimestamp + 120
      },
      {
        id: `msg_${uuidv4()}`,
        role: "assistant",
        content_transcript: "I'd be happy to email you the full inspection report. Could you please confirm the email address where you'd like to receive it?",
        created_at: baseTimestamp + 125
      }
    ];

    // Insert messages into the database
    console.log('Inserting messages into the database...');
    for (const message of conversation) {
      const fullMessage = {
        ...message,
        session_id: sessionId,
        content_type: "text",
        object: "realtime.item",
        status: "completed",
        type: "message"
      };

      // Insert directly using SQL query
      try {
        await sql.query(
          "INSERT INTO messages (created_at, id, session_id, content_type, content_transcript, object, role, status, type) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) ON CONFLICT (id) DO UPDATE SET content_transcript = $5, status = $8",
          [
            fullMessage.created_at,
            fullMessage.id,
            fullMessage.session_id,
            fullMessage.content_type || "text",
            fullMessage.content_transcript || "",
            fullMessage.object || "realtime.item",
            fullMessage.role,
            fullMessage.status || "completed",
            fullMessage.type || "message",
          ]
        );
        console.log(`Inserted message: ${message.role} - ${message.content_transcript.substring(0, 30)}...`);
      } catch (error) {
        console.error(`Error inserting message: ${error.message}`);
        throw error;
      }
    }

    // Verify messages were inserted
    console.log('\nVerifying inserted messages...');

    try {
      // Query directly using the same connection
      const messagesRes = await sql.query(
        "SELECT * FROM messages WHERE session_id = $1 ORDER BY created_at ASC",
        [sessionId]
      );

      // Log the full response structure to understand what we're getting
      console.log('Full response structure:', Object.keys(messagesRes));

      // Try to access rows in different ways
      let retrievedMessages = [];

      if (messagesRes && typeof messagesRes === 'object') {
        if (Array.isArray(messagesRes)) {
          // If messagesRes is an array, use it directly
          retrievedMessages = messagesRes;
          console.log('messagesRes is an array with length:', messagesRes.length);
        } else if (messagesRes.rows && Array.isArray(messagesRes.rows)) {
          // If messagesRes has a rows property that's an array, use that
          retrievedMessages = messagesRes.rows;
          console.log('messagesRes.rows is an array with length:', messagesRes.rows.length);
        } else {
          // Try to extract data in another way
          console.log('messagesRes structure:', JSON.stringify(messagesRes).substring(0, 500));

          // If it's JSON-like with data, try to extract it
          if (messagesRes.data && Array.isArray(messagesRes.data)) {
            retrievedMessages = messagesRes.data;
          }
        }
      }

      // Debug the response
      console.log('Query result sample:', JSON.stringify(messagesRes).substring(0, 300) + '...');
      console.log(`Retrieved ${retrievedMessages.length} messages for session ${sessionId}`);

      if (retrievedMessages && retrievedMessages.length === conversation.length) {
        console.log('\nSuccess! All messages were inserted correctly.');
        console.log(`\nConversation session ID: ${sessionId}`);
        console.log('You can view this conversation in the app by navigating to:');
        console.log(`http://localhost:3000/c/${sessionId}`);
      } else {
        console.error(`\nError: Expected ${conversation.length} messages but found ${retrievedMessages ? retrievedMessages.length : 0}`);
      }
    } catch (error) {
      console.error(`Error retrieving messages: ${error.message}`);
      throw error;
    }

  } catch (error) {
    console.error('Error generating conversation:', error);
    process.exit(1);
  }
}

// Run the script
generateConversation();
