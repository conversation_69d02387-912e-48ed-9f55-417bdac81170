# Repository Cleanup Instructions

## Overview

This document provides instructions for cleaning up the repository to focus on the ElevenLabs voice assistant functionality for successful deployment on Vercel.

## Non-Essential Components

I've created a `non-essential` directory in the project root where you should move the following components that aren't directly related to the ElevenLabs voice assistant:

1. **Entire Directories:**
   - `/mcp-client-chatbot/` - Separate chatbot implementation
   - `/agents/` - LiveKit agent implementation
   - `/elevenlabs-mcp/` - MCP-related code
   - `/app/copilot-demo/` - Demo implementation

2. **Docker Files:**
   - `Dockerfile`
   - `Dockerfile.agent`
   - `Dockerfile.web`
   - `docker-compose.yml`
   - `docker-compose.prod.yml`
   - `docker-build.sh`
   - `livekit.yaml.example`

3. **UI Components:**
   - `/components/kokonutui/` - UI components not directly related to voice
   - `/components/IPhoneMockup.tsx` - No longer used in PWA mode

## Moving Process

To move these files to the non-essential directory:

```bash
# Create directories in non-essential
mkdir -p non-essential/components/kokonutui
mkdir -p non-essential/app/copilot-demo
mkdir -p non-essential/agents
mkdir -p non-essential/elevenlabs-mcp

# Move directories
mv mcp-client-chatbot non-essential/
mv agents/* non-essential/agents/
mv elevenlabs-mcp/* non-essential/elevenlabs-mcp/
mv app/copilot-demo/* non-essential/app/copilot-demo/
mv components/kokonutui/* non-essential/components/kokonutui/
mv components/IPhoneMockup.tsx non-essential/components/

# Move Docker files
mv Dockerfile* non-essential/
mv docker-compose*.yml non-essential/
mv docker-build.sh non-essential/
mv livekit.yaml.example non-essential/
```

## Adjusting Imports

After moving the files, you'll need to adjust import paths in the following files:

1. In `app/c/[slug]/page.tsx`:
   - Remove the IPhoneMockup import: `import { IPhoneMockup } from "@/components/IPhoneMockup"`
   - Update the IPhoneMockup component usage (currently wrapped in a conditional, lines 328-425)

2. In `components/TextAnimation.tsx`:
   - If there are any references to removed components, update or remove them

## PWA Configuration

The PWA configuration is correctly set up with:

1. **Service Worker:**
   - Configured in `v0-user-next.config.js` using `next-pwa`
   - Service worker is automatically generated during build

2. **Manifest:**
   - `manifest.json` is properly configured
   - Uses SVG icons (consider replacing with PNG as mentioned in `PWA-IMPLEMENTATION.md`)

3. **Meta Tags:**
   - Configured in `app/layout.tsx`

## Essential Voice Assistant Components

These are the essential components for the ElevenLabs voice assistant that should be kept:

1. **ElevenLabs Integration:**
   - `/lib/elevenlabs.ts`
   - `/lib/elevenlabs-fallback.ts`
   - `/components/ElevenLabsCopilotProvider.tsx`
   - `/components/VoiceCopilotProvider.tsx`
   - `/components/VoiceAssistantCard.tsx`
   - `/components/VoiceAssistantResponseCard.tsx`
   - `/components/VoiceInterface.tsx`
   - `/app/api/i/route.ts`
   - `/types/elevenlabs-react.d.ts`

2. **Voice UI Components:**
   - `/components/TextAnimation.tsx`
   - `/data/voiceAssistantQAData.tsx`
   - `/components/Message.tsx`

3. **Conversation Pages:**
   - `/app/c/[slug]/page.tsx`
   - `/app/c/[slug]/lib/mock-elevenlabs.tsx`
   - `/app/c/[slug]/mock-elevenlabs.tsx`

4. **PWA Configuration:**
   - `/v0-user-next.config.js`
   - `/public/manifest.json`
   - `/public/icon-svg-192.svg`
   - `/public/icon-svg-512.svg`

## Database Configuration

The app uses NeonDB (Postgres) with a fallback to in-memory storage when the database isn't available. You need to set the following environment variables in Vercel:

```
DATABASE_URL="postgres://user:password@localhost:5432/voice_assistant"
AGENT_ID="your-elevenlabs-agent-id"
XI_API_KEY="your-elevenlabs-api-key"
```