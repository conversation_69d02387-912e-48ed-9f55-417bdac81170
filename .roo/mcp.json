{"mcpServers": {"pica": {"command": "node", "args": ["/Users/<USER>/pica-mcp/build/src/index.js"], "env": {"PICA_SECRET": "sk_live_1_BN8d_f0CnDdATlyMEshDfhqEygxH4_gXAz-O_aygDIWyqcVQm8O9zEbgZw6wGdFeq5xPKrVkSUoB1a_csH32-b7c7U51yp8JxUVk9aEWaXSAD4mzNxYpYwtv71WTCfGva08KBCOhnOzR7WpUN4zoT0yn2vnXQh7baP_ECPy5cFmxm4XpRCJbFYS68Gf-h0YAr4vA-V6CqAnNmVXRJYWemIIbNVgXKEvE9RoBFcI0Bg"}}, "desktop-commander": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@wonderwhy-er/desktop-commander", "--key", "da40360b-49b2-46b7-84f5-3f9641931b16"]}, "xcode-mcp-server": {"command": "node", "args": ["/Users/<USER>/xcode-xtras/xcode-mcp-server/dist/index.js"], "env": {"PROJECTS_BASE_DIR": "/Users/<USER>/Projects"}}, "figma": {"command": "/opt/homebrew/bin/figma-mcp-pro", "args": [], "env": {"FIGMA_PERSONAL_ACCESS_TOKEN": "figd_mm7jebrmRohJTfKX8zjkvJ9Oi8jPaPUwMoZFjyU-"}}, "ElevenLabs": {"command": "/Users/<USER>/Projects/OpenAI-AgentsSDK-GUI/elevenlabs-mcp/venv/bin/python", "args": ["/Users/<USER>/Projects/OpenAI-AgentsSDK-GUI/elevenlabs-mcp/elevenlabs_mcp/server.py"], "env": {"ELEVENLABS_API_KEY": "***************************************************"}}, "context7-mcp": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@upstash/context7-mcp", "--key", "da40360b-49b2-46b7-84f5-3f9641931b16"]}, "mcp-shrimp-task-manager": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@cjo4m06/mcp-shrimp-task-manager", "--key", "da40360b-49b2-46b7-84f5-3f9641931b16", "--profile", "inner-turkey-86QgIO"]}, "Toolbase": {"command": "/Users/<USER>/.toolbase/toolbase-runner", "args": ["-p=proxy", "-f=/Users/<USER>/.toolbase/config.json", "-v=windsurf", "-l=/Users/<USER>/Library/Logs/Toolbase/windsurf-toolbase-proxy.log"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {}}, "linear-mcp-server": {"command": "npx", "args": ["-y", "mcp-remote", "https://mcp.linear.app/sse"], "env": {}}, "taskmaster-ai": {"command": "npx", "args": ["-y", "--package=task-master-ai", "task-master-ai"], "env": {"ANTHROPIC_API_KEY": "YOUR_ANTHROPIC_API_KEY_HERE", "PERPLEXITY_API_KEY": "YOUR_PERPLEXITY_API_KEY_HERE", "OPENAI_API_KEY": "YOUR_OPENAI_API_KEY_HERE", "GOOGLE_API_KEY": "YOUR_GOOGLE_API_KEY_HERE", "MISTRAL_API_KEY": "YOUR_MISTRAL_API_KEY_HERE", "OPENROUTER_API_KEY": "YOUR_OPENROUTER_API_KEY_HERE", "XAI_API_KEY": "YOUR_XAI_API_KEY_HERE", "AZURE_OPENAI_API_KEY": "YOUR_AZURE_API_KEY_HERE", "TASKMASTER_PROJECT_TYPE": "rork-askara", "TASKMASTER_AUTO_BRANCH_TAGS": "true", "TASKMASTER_SMART_ROUTING": "true", "TASKMASTER_RESEARCH_AUTO_SAVE": "true", "TASKMASTER_COMPLEXITY_THRESHOLD": "6"}}, "mcp-supermemory-ai": {"command": "npx", "args": ["-y", "supergateway", "--sse", "https://mcp.supermemory.ai/M397a-_6Ame_2b8aN8e9k/sse"]}}}