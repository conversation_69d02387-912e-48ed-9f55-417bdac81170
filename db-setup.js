// A simple script to call the setup API
const fs = require('fs');

async function setupDatabase() {
  try {
    console.log('Setting up database...');

    // Manually set the DATABASE_URL environment variable from .env or .env.local
    let envFile;
    try {
      envFile = fs.readFileSync('.env.local', 'utf-8');
    } catch (err) {
      try {
        envFile = fs.readFileSync('.env', 'utf-8');
      } catch (err2) {
        console.error('Neither .env nor .env.local file found');
        process.exit(1);
      }
    }

    const dbUrlMatch = envFile.match(/DATABASE_URL="([^"]+)"/);

    if (!dbUrlMatch) {
      console.error('DATABASE_URL not found in environment files');
      process.exit(1);
    }

    // Set environment variable for other modules that might use it
    process.env.DATABASE_URL = dbUrlMatch[1];

    // Directly use the Neon database
    const { neon, neonConfig } = require('@neondatabase/serverless');

    // Configure neon
    neonConfig.poolQueryViaFetch = true;
    neonConfig.fetchTimeout = 10000;
    neonConfig.fetchRetry = 3;
    neonConfig.fetchRetryMaxDelay = 2000;

    const databaseUrl = process.env.DATABASE_URL;
    console.log('Using database URL:', databaseUrl.substring(0, 25) + '...');

    if (!databaseUrl) {
      console.error('DATABASE_URL environment variable not found');
      process.exit(1);
    }

    const sql = neon(databaseUrl);

    // Check if table exists with debugging
    console.log('Checking if messages table exists...');
    try {
      const tableExists = await sql.query(
        "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)",
        ["messages"]
      );

      console.log('Table exists query result:', JSON.stringify(tableExists, null, 2));

      // Safely check if the table exists
      const exists = tableExists &&
                    tableExists.rows &&
                    tableExists.rows[0] &&
                    tableExists.rows[0].exists === true;

      if (!exists) {
        console.log('Creating messages table...');
        try {
          // Create the messages table
          await sql.query(
            "CREATE TABLE IF NOT EXISTS messages (created_at INTEGER, id TEXT PRIMARY KEY, session_id TEXT NOT NULL, content_type TEXT, content_transcript TEXT, object TEXT, role TEXT NOT NULL, status TEXT, type TEXT)"
          );

          // Create an index for faster queries
          await sql.query("CREATE INDEX IF NOT EXISTS idx_session_created_at ON messages (session_id, created_at)");
          console.log('Tables created successfully!');
        } catch (createError) {
          console.error('Error creating table:', createError);
          throw createError;
        }
      } else {
        console.log('Messages table already exists');
      }

      // Test the connection by inserting and retrieving a test message
      console.log('Testing database with a sample message...');
      const testId = 'test-' + Date.now();
      try {
        await sql.query(
          "INSERT INTO messages (created_at, id, session_id, role, content_transcript) VALUES ($1, $2, $3, $4, $5)",
          [Math.floor(Date.now() / 1000), testId, 'test-session', 'system', 'Database setup complete']
        );

        const retrieved = await sql.query("SELECT * FROM messages WHERE id = $1", [testId]);
        console.log('Test message retrieved:', JSON.stringify(retrieved && retrieved.rows && retrieved.rows[0], null, 2));

        // Clean up the test message
        await sql.query("DELETE FROM messages WHERE id = $1", [testId]);
        console.log('Database setup verification complete!');
      } catch (testError) {
        console.error('Error testing database:', testError);
        throw testError;
      }
    } catch (tableError) {
      console.error('Error checking if table exists:', tableError);
      throw tableError;
    }
  } catch (error) {
    console.error('Error setting up database:', error);
    process.exit(1);
  }
}

// Run the setup
setupDatabase();