/** @type {import('tailwindcss').Config} */
const config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        emerald: {
          900: "rgb(6, 78, 59)",
          500: "rgb(16, 185, 129)",
          400: "rgb(52, 211, 153)",
          300: "rgb(110, 231, 183)",
        },
        blue: {
          900: "rgb(30, 58, 138)",
          500: "rgb(59, 130, 246)",
          400: "rgb(96, 165, 250)",
          300: "rgb(147, 197, 253)",
        },
        violet: {
          900: "rgb(76, 29, 149)",
          500: "rgb(139, 92, 246)",
          400: "rgb(167, 139, 250)",
          300: "rgb(196, 181, 253)",
        },
        amber: {
          900: "rgb(120, 53, 15)",
          500: "rgb(245, 158, 11)",
          400: "rgb(251, 191, 36)",
          300: "rgb(252, 211, 77)",
        },
        indigo: {
          900: "rgb(49, 46, 129)",
          500: "rgb(99, 102, 241)",
          400: "rgb(129, 140, 248)",
          300: "rgb(165, 180, 252)",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
}

export default config

