# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a voice assistant application built with Next.js that features a conversational interface with ElevenLabs integration for voice capabilities. The main components include:

- Voice Assistant Cards for displaying Q&A content
- Voice interface for conversations 
- Database integration (with in-memory fallback) for storing conversation history
- Mobile-friendly design with iPhone mockup display

## Commands

### Development

```bash
# Start the development server
npm run dev

# Build the project
npm run build

# Start the production server
npm run start

# Run linting
npm run lint
```

## Architecture

### Key Components

1. **Voice Interface**: The application uses ElevenLabs Conversational AI 2.0 through the `@11labs/react` package.
   - `conversation.tsx` - Main conversation component using `useConversation` hook
   - Real-time voice conversations with AI agents
   - Visual feedback for voice activity (speaking/listening states)

2. **Data Storage**: 
   - The app uses NeonDB (Postgres) for storing conversation history
   - Includes fallback to in-memory storage when database isn't available
   - Environment variables: `DATABASE_URL` is required for persistent storage

3. **Routing Structure**:
   - `/` - Main entry point with auto-login
   - `/c/[slug]` - Conversation page with unique ID
   - `/history` - Conversation history page
   - `/api/*` - Backend API routes for ElevenLabs integration
   - `/api/get-signed-url` - Generates signed URLs for private agent authentication

4. **UI Components**:
   - `VoiceAssistantCard.tsx` - Card component for displaying voice assistant Q&A
   - `IPhoneMockup.tsx` - Mobile device mockup for displaying the interface
   - `TextAnimation.tsx` - Animated text component for the voice assistant
   - `conversation.tsx` - ElevenLabs Conversational AI component with visual feedback

### Data Flow

1. User clicks microphone button to start conversation
2. Browser requests microphone permission
3. App fetches signed URL from `/api/get-signed-url` for authentication
4. WebSocket connection established with ElevenLabs Conversational AI
5. Real-time audio streaming between user and AI agent
6. Visual feedback shows speaking/listening states
7. Transcripts are captured and can be saved to database
8. Responses are displayed in the UI with visual feedback

## Environment Setup

The application requires the following environment variables:

```
# Database connection (optional, falls back to in-memory)
DATABASE_URL="postgres://user:password@localhost:5432/voice_assistant"

# ElevenLabs API (required for voice functionality)
AGENT_ID="your-elevenlabs-agent-id"
XI_API_KEY="your-elevenlabs-api-key"
```

1. `DATABASE_URL`: Connection string for a Postgres database (optional, falls back to in-memory storage)
2. `AGENT_ID`: ElevenLabs agent ID for the voice assistant
3. `XI_API_KEY`: ElevenLabs API key for authentication

## Testing

No specific testing commands are defined in the codebase. Add tests and corresponding commands as needed.

## MCP Tools Integration

The application now supports ElevenLabs MCP (Model Context Protocol) tool calling:

### Client Tools
Client-side tools that execute directly in the browser:
- **Email Operations**: Open drafts, fill fields, send emails
- **Browser Actions**: Open URLs, show notifications
- **Data Operations**: Copy to clipboard
- **MCP Bridge**: Invoke server-side MCP tools

### MCP Tools
Server-side tools executed through the `/api/mcp-bridge` endpoint:
- **Voice & Audio**: Text-to-speech, speech-to-text, sound effects
- **Voice Management**: Search and manage voices
- **Agent Management**: Create and manage conversational AI agents

### Configuration
Add to `.env` for MCP bridge security:
```
MCP_SECURITY_TOKEN="your-secure-token-here"
```

### Tool Execution UI
Real-time tool execution status is displayed in the bottom-right corner with:
- Execution status indicators
- Expandable details for arguments and results
- Error reporting

See `MCP-TOOLS-INTEGRATION.md` for detailed documentation.