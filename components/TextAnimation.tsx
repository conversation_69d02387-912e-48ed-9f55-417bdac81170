"use client"

import { useTypingEffect } from "@/components/useTypingEffect"
import { motion, AnimatePresence } from "framer-motion"
import { useEffect, useState, useRef, ReactNode } from "react"

import useLocalSTT from "@/hooks/useLocalSTT"
import Message from "@/components/Message"
import VoiceAssistantResponseCard from "@/components/VoiceAssistantResponseCard"
import AIInput_08 from "./AIInput_08"
import { cn } from "@/lib/utils"
import { History, X } from "lucide-react"
import ToolCardDispatcher from "./ToolCardDispatcher"
import { useStream } from "@/hooks/useStream"

type AIState = "idle" | "listening" | "speaking"

interface Props {
  onStartListening?: () => void
  onStopListening?: () => void
  isAudioPlaying?: boolean
  currentText: string
  messages: any[]
  onHistoryClick?: () => void
  floatAtBottom?: boolean
  useStreamingApi?: boolean
}

export default function AiTalkingAnimation({
  onStartListening,
  onStopListening,
  isAudioPlaying = false,
  currentText,
  messages: initialMessages = [],
  onHistoryClick,
  floatAtBottom = false,
  useStreamingApi = false,
}: Props) {
  const [aiState, setAiState] = useState<AIState>("idle")
  const [activeMessageId, setActiveMessageId] = useState<string | null>(null)
  const animatedCurrentText = useTypingEffect(currentText, 20)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Local interim STT while listening
  const { interim } = useLocalSTT(aiState === "listening")
  
  // Use streaming API if enabled
  const {
    messages: streamMessages = [],
    transcript,
    isStreaming,
    startStream,
    error: streamError,
  } = useStreamingApi ? useStream('/api/chat/stream') : {
    messages: [],
    transcript: '',
    isStreaming: false,
    startStream: null,
    error: null,
  }

  // Combine messages from props and streaming if needed
  const messages = useStreamingApi ? streamMessages : initialMessages

  const handleButtonClick = () => {
    if (aiState === "listening" || aiState === "speaking") {
      onStopListening?.()
      setAiState("idle")
    } else if (!isAudioPlaying) {
      if (useStreamingApi && startStream) {
        // For testing with text input when using streaming API
        const userInput = prompt('Enter your message');
        if (userInput) {
          startStream(userInput);
          setAiState("listening");
        }
      } else {
        onStartListening?.();
        setAiState("listening");
      }
    }
  }

  useEffect(() => {
    if (isAudioPlaying) {
      setAiState("speaking")
    } else if (aiState === "speaking" && currentText) {
      setAiState("idle")
    }
  }, [isAudioPlaying, aiState, currentText])

  // Scroll to bottom when messages update
  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = 0 // Scroll to top since we're displaying messages in reverse
    }

    // Set the most recent message as active
    if (messages.length > 0) {
      // Use the last array index to uniquely identify the active element even
      // if duplicate ids exist (e.g. when history and live updates collide)
      const lastIndex = messages.length - 1
      setActiveMessageId(`${messages[lastIndex].id}-${lastIndex}`)
    }
  }, [messages])

  // Animation variants for the container
  const containerVariants = {
    idle: {
      justifyContent: "center",
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 30,
      },
    },
    active: {
      justifyContent: "flex-start",
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 30,
      },
    },
  }

  // Animation variants for the button
  const buttonVariants = {
    idle: {
      x: 0,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 25,
      },
    },
    active: {
      x: 0,
      scale: [0.95, 1.05, 1],
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 25,
        scale: {
          duration: 0.35,
          times: [0, 0.6, 1],
        },
      },
    },
  }

  // Animation variants for the text
  const textVariants = {
    hidden: {
      opacity: 0,
      x: -20,
      transition: {
        duration: 0.2,
      },
    },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 25,
        delay: 0.1,
      },
    },
  }

  // Reverse the messages array to display newest at the top
  const reversedMessages = [...messages].reverse()

  return (
    <div className="flex flex-col h-full bg-zinc-900 p-4">
      {/* Messages container - flex-col-reverse makes new messages appear at the bottom */}
      <div
        ref={containerRef}
        className={cn(
          "flex-1 overflow-y-auto pr-1 conversation-container",
          floatAtBottom ? "pb-32" : "mb-4",
        )}
      >
        <div className="flex flex-col-reverse space-y-reverse space-y-6">
          <div ref={messagesEndRef} />
          {reversedMessages.map((conversationItem, index) => {
            const itemKey = `${conversationItem.id}-${index}`

            // Format timestamp if available
            let timestamp: string | undefined = undefined
            if (conversationItem.created_at) {
              timestamp = new Date(conversationItem.created_at * 1000).toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              })
            }

            const motionProps = {
              initial: { opacity: 0, y: 50 },
              animate: { opacity: 1, y: 0 },
              transition: { duration: 0.4, ease: "easeOut" },
            }

            // Handle tool cards from streaming API
            if (conversationItem.role === 'tool') {
              return (
                <motion.div key={itemKey} {...motionProps}>
                  <ToolCardDispatcher
                    name={conversationItem.name}
                    args={conversationItem.args}
                  />
                </motion.div>
              )
            }

            // Render assistant responses with the fancy card, otherwise use the
            // standard message bubble for user input. A user message can also
            // contain `formatted.transcript`, so we additionally check the
            // role to avoid showing user messages inside assistant cards.
            if (
              conversationItem.role === 'assistant' &&
              conversationItem.formatted?.transcript
            ) {
              return (
                <motion.div key={itemKey} {...motionProps}>
                  <VoiceAssistantResponseCard
                    content={conversationItem.formatted.transcript}
                    timestamp={timestamp}
                    isActive={itemKey === activeMessageId}
                  />
                </motion.div>
              )
            }

            return (
              <motion.div key={itemKey} {...motionProps}>
                <Message conversationItem={conversationItem} isActive={itemKey === activeMessageId} />
              </motion.div>
            )
          })}
        </div>
      </div>

      {/* Navigation bar / input container */}
      <motion.div
        className={cn(
          floatAtBottom
            ? "fixed left-1/2 -translate-x-1/2 bottom-4 z-50 w-[calc(100%-2rem)] max-w-xl"
            : "relative w-full mb-4",
          "backdrop-blur-sm shadow-lg border border-zinc-700/50",
          "glassmorphism glass-inner-shadow",
          "rounded-2xl flex items-center h-16 overflow-hidden",
        )}
        animate={aiState === "idle" ? "idle" : "active"}
        variants={containerVariants}
      >
        {/* AI Input Button */}
        <motion.div
          className={cn(
            "flex-shrink-0 hover:bg-white/10 rounded-2xl",
            aiState === "idle" && "mx-auto",
          )}
          variants={buttonVariants}
          animate={aiState === "idle" ? "idle" : "active"}
        >
          <AIInput_08
            isActive={aiState === "listening" || aiState === "speaking"}
            onClick={handleButtonClick}
            isDemo={false}
          />
        </motion.div>

        {/* Text content - only shown when active */}
        <AnimatePresence>
          {aiState !== "idle" && (
            <motion.div
              className="flex-1 ml-4 mr-24"
              initial="hidden"
              animate="visible"
              exit="hidden"
              variants={textVariants}
            >
              {/* Transcription text with fade-out at top */}
              <div className="relative h-6 overflow-hidden">
                <p
                  className="text-zinc-100 font-mono text-sm whitespace-pre-wrap"
                  aria-live="polite"
                >
                  {aiState === "listening"
                    ? interim || "Listening…"
                    : aiState === "speaking"
                    ? animatedCurrentText
                    : ""}
                </p>
                {/* Top fade overlay */}
                <div className="pointer-events-none absolute inset-x-0 top-0 h-3 bg-gradient-to-b from-zinc-900 to-transparent" />
              </div>
              {aiState === "listening" && (
                <motion.div
                  animate={{
                    opacity: [0, 1, 0],
                  }}
                  transition={{
                    duration: 0.8,
                    repeat: Number.POSITIVE_INFINITY,
                    ease: "easeInOut",
                  }}
                  className="bg-blue-600 mt-2 h-5 w-2"
                />
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Conversation History / Cancel Button */}
        <motion.div
          className="flex-shrink-0 hover:bg-white/10 rounded-2xl absolute right-2 top-1/2 -translate-y-1/2"
        >
          <button
            type="button"
            onClick={() => {
              if (aiState === "idle") {
                onHistoryClick?.()
              } else {
                onStopListening?.()
                setAiState("idle")
              }
            }}
            className={cn(
              "relative w-16 h-16 rounded-2xl flex items-center justify-center transition-all duration-500",
              "glassmorphism",
              aiState === "idle"
                ? "border border-transparent"
                : "bg-red-600/30 border border-red-500/50",
            )}
          >
            {/* Outer glow */}
            <div
              className={cn(
                "absolute -inset-2 rounded-2xl blur-xl opacity-70",
                aiState === "idle"
                  ? "bg-gradient-to-r from-blue-500/10 via-blue-500/10 to-blue-500/10"
                  : "bg-gradient-to-r from-red-500/10 via-red-500/10 to-red-500/10",
              )}
            />

            {/* Icon */}
            {aiState === "idle" ? (
              <History className="w-6 h-6 text-white relative z-10" />
            ) : (
              <X className="w-6 h-6 text-red-500 relative z-10" />
            )}

            {/* Bottom reflection */}
            <div className="absolute bottom-0 left-0 right-0 h-1/3 rounded-b-2xl bg-gradient-to-t from-white/5 to-transparent pointer-events-none" />
          </button>
        </motion.div>
      </motion.div>
    </div>
  )
}
