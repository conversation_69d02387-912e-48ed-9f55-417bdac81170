"use client";

import "@copilotkit/react-ui/styles.css";
import { ReactNode } from "react";
import { CopilotKit } from "@copilotkit/react-core";
import { CopilotSidebar } from "@copilotkit/react-ui";

interface CopilotKitProviderProps {
  children: ReactNode;
  enableSidebar?: boolean;
  sidebarProps?: {
    defaultOpen?: boolean;
    clickOutsideToClose?: boolean;
    labels?: {
      title?: string;
      initial?: string;
    };
  };
}

export default function CopilotKitProvider({
  children,
  enableSidebar = true,
  sidebarProps = {
    defaultOpen: false,
    clickOutsideToClose: true,
    labels: {
      title: "Voice Assistant",
      initial: "👋 Hi there! How can I help you today?"
    }
  }
}: CopilotKitProviderProps) {
  // CopilotKit public API key
  const publicApiKey = "ck_pub_4adc457c723a7b00b6fd291a2fc96bf0";
  // Theme color for CopilotKit UI elements
  const themeColor = "#6366f1";

  return (
    <CopilotKit
      publicApiKey={publicApiKey}
    >
      <div style={{ "--copilot-kit-primary-color": themeColor } as any}>
        {children}
        
        {enableSidebar && (
          <CopilotSidebar
            defaultOpen={sidebarProps.defaultOpen}
            clickOutsideToClose={sidebarProps.clickOutsideToClose}
            labels={{
              title: sidebarProps.labels?.title || "Voice Assistant",
              initial: sidebarProps.labels?.initial || "👋 Hi there! How can I help you today?"
            }}
          />
        )}
      </div>
    </CopilotKit>
  );
}