"use client"

import { <PERSON><PERSON>, User } from "lucide-react"
import { cn } from "@/lib/utils"
import { useState, useEffect } from "react"

interface MessageProps {
  conversationItem: {
    id: string
    role: string
    formatted?: {
      transcript: string
    }
    content?: string
    created_at?: number
  }
  isActive?: boolean
}

export default function Message({ conversationItem, isActive = false }: MessageProps) {
  const isUser = conversationItem.role === "user"

  // Client-side timestamp formatting using useState and useEffect
  const [timestamp, setTimestamp] = useState<string | undefined>(undefined)
  
  useEffect(() => {
    if (conversationItem.created_at) {
      setTimestamp(new Date(conversationItem.created_at * 1000).toLocaleTimeString([], { 
        hour: "2-digit", 
        minute: "2-digit" 
      }))
    }
  }, [conversationItem.created_at])

  return (
    <div className={`flex flex-row items-start gap-3 ${isUser ? "justify-end" : "justify-start"}`}>
      {!isUser && (
        <div
          className={cn(
            "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
            isActive ? "bg-blue-500" : "bg-blue-600",
          )}
        >
          <Cpu className="w-4 h-4 text-white" />
        </div>
      )}

      <div
        className={cn(
          "max-w-[80%] rounded-2xl px-4 py-3 text-white shadow-md transition-all duration-300",
          isUser
            ? isActive
              ? "bg-blue-500 shadow-[0_0_15px_-5px_rgba(59,130,246,0.5)]"
              : "bg-blue-600"
            : isActive
              ? "bg-zinc-700 shadow-[0_0_15px_-5px_rgba(82,82,91,0.5)]"
              : "bg-zinc-800",
        )}
      >
        <p className="text-sm">{conversationItem.formatted?.transcript || conversationItem.content || ""}</p>
        {timestamp && <p className="text-xs text-zinc-400 mt-1 text-right">{timestamp}</p>}
      </div>

      {isUser && (
        <div
          className={cn(
            "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
            isActive ? "bg-blue-500" : "bg-blue-600",
          )}
        >
          <User className="w-4 h-4 text-white" />
        </div>
      )}
    </div>
  )
}

