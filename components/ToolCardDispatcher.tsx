"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Rain, Utensils } from "lucide-react"
import VoiceAssistantCard from "./VoiceAssistantCard"
import WeatherCard from "./WeatherCard"
import InspectionRatingCard from "./InspectionRatingCard"

interface ToolMsg {
  name: string
  args: any
}

export default function ToolCardDispatcher({ name, args }: ToolMsg) {
  switch (name) {
    case 'show_weather':
      return <WeatherCard {...args} />
    case 'show_inspection_rating':
      return <InspectionRatingCard {...args} />
    default:
      return <VoiceAssistantCard data={{
        question: `Unknown tool: ${name}`,
        answer: JSON.stringify(args, null, 2),
        source: 'internal',
        icon: <AlertTriangle className="w-6 h-6 text-amber-400" />,
        status: 'error',
        tags: ['tool'],
      }} />
  }
}
