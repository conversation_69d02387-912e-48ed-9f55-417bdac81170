'use client';

import { useConversation } from '@elevenlabs/react';
import { useCallback, useState, useEffect, useRef } from 'react';
import { Mi<PERSON>, MicOff, AlertCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAudioManager, checkMicrophoneSupport } from '@/hooks/useAudioManager';

interface ConversationProps {
  onMessage?: (message: string) => void;
  className?: string;
}

export function Conversation({ onMessage, className }: ConversationProps) {
  const [error, setError] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [time, setTime] = useState(0);
  const [isClient, setIsClient] = useState(false);
  const [microphonePermission, setMicrophonePermission] = useState<'granted' | 'denied' | 'prompt' | 'unknown'>('unknown');
  const permissionCheckRef = useRef<boolean>(false);

  // Use centralized audio manager to prevent conflicts
  const audioManager = useAudioManager('ElevenLabsConversation');

  // Enhanced logging for debugging microphone issues
  const logMicrophoneEvent = useCallback((event: string, details?: any) => {
    if (process.env.NEXT_PUBLIC_ENABLE_VOICE_DEBUG === 'true') {
      console.log(`[MIC DEBUG] ${event}:`, details);
    }
  }, []);

  const conversation = useConversation({
    onConnect: () => {
      logMicrophoneEvent('ElevenLabs Connected');
      setError(null);
      setIsConnecting(false);
    },
    onDisconnect: () => {
      logMicrophoneEvent('ElevenLabs Disconnected');
      setIsConnecting(false);
      // Release microphone through audio manager
      audioManager.releaseMicrophone();
    },
    onMessage: (message) => {
      logMicrophoneEvent('Message received', { type: message.type, hasText: !!message.text });
      if (message.type === 'user_transcript' && message.text && onMessage) {
        onMessage(message.text);
      }
    },
    onError: (error) => {
      logMicrophoneEvent('Conversation error', error);
      setError(error.message || 'An error occurred');
      setIsConnecting(false);
    },
  });

  // Check microphone permissions and browser compatibility on mount
  useEffect(() => {
    setIsClient(true);

    const checkMicrophonePermission = async () => {
      if (permissionCheckRef.current) return; // Prevent multiple checks
      permissionCheckRef.current = true;

      // First check browser compatibility
      const compatibility = checkMicrophoneSupport();
      if (!compatibility.supported) {
        logMicrophoneEvent('Browser compatibility failed', compatibility.error);
        setError(compatibility.error);
        return;
      }

      try {
        if (navigator.permissions) {
          const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
          logMicrophoneEvent('Initial permission state', permission.state);
          setMicrophonePermission(permission.state);

          // Listen for permission changes
          permission.onchange = () => {
            logMicrophoneEvent('Permission changed', permission.state);
            setMicrophonePermission(permission.state);
          };
        }
      } catch (error) {
        logMicrophoneEvent('Permission check failed', error);
      }
    };

    checkMicrophonePermission();
  }, [logMicrophoneEvent]);

  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (conversation.status === 'connected') {
      setTime(0);
      intervalId = setInterval(() => {
        setTime((t) => t + 1);
      }, 1000);
    } else {
      setTime(0);
    }

    return () => clearInterval(intervalId);
  }, [conversation.status]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  const getSignedUrl = async (): Promise<string> => {
    const response = await fetch("/api/get-signed-url");
    if (!response.ok) {
      throw new Error(`Failed to get signed url: ${response.statusText}`);
    }
    const { signedUrl } = await response.json();
    return signedUrl;
  };

  const startConversation = useCallback(async () => {
    try {
      setError(null);
      setIsConnecting(true);
      logMicrophoneEvent('Starting conversation');

      // Use audio manager to request microphone access
      const stream = await audioManager.requestMicrophone({
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
        sampleRate: 16000,
        channelCount: 1
      });

      // Get signed URL for authentication
      const signedUrl = await getSignedUrl();
      logMicrophoneEvent('Got signed URL');

      // Start the conversation with your agent
      await conversation.startSession({
        signedUrl,
      });

      logMicrophoneEvent('Conversation started successfully');

    } catch (error) {
      logMicrophoneEvent('Failed to start conversation', error);
      setError(error instanceof Error ? error.message : 'Failed to start conversation');
      setIsConnecting(false);
      // Release microphone on error
      audioManager.releaseMicrophone();
    }
  }, [conversation, logMicrophoneEvent, audioManager]);

  const stopConversation = useCallback(async () => {
    logMicrophoneEvent('Stopping conversation');
    await conversation.endSession();
    // Audio manager will handle cleanup in onDisconnect
  }, [conversation, logMicrophoneEvent]);

  return (
    <div className={cn("flex flex-col items-center gap-4", className)}>
      <button
        onClick={conversation.status === 'connected' ? stopConversation : startConversation}
        disabled={isConnecting}
        className={cn(
          "group w-16 h-16 rounded-xl flex items-center justify-center transition-all",
          conversation.status === 'connected'
            ? "bg-red-500/20 hover:bg-red-500/30"
            : "bg-white/10 hover:bg-white/20",
          error && "ring-2 ring-red-500",
          isConnecting && "opacity-50 cursor-not-allowed"
        )}
      >
        {isConnecting ? (
          <Loader2 className="w-6 h-6 text-white animate-spin" />
        ) : error ? (
          <AlertCircle className="w-6 h-6 text-red-500" />
        ) : conversation.status === 'connected' ? (
          <div className="relative">
            <MicOff className="w-6 h-6 text-white" />
            {conversation.isSpeaking && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse" />
            )}
          </div>
        ) : (
          <Mic className="w-6 h-6 text-white/70" />
        )}
      </button>

      {conversation.status === 'connected' && (
        <span className={cn(
          "font-mono text-sm transition-opacity duration-300 text-white/70"
        )}>
          {formatTime(time)}
        </span>
      )}

      <div className="h-4 w-64 flex items-center justify-center gap-0.5">
        {[...Array(48)].map((_, i) => (
          <div
            key={i}
            className={cn(
              "w-0.5 rounded-full transition-all duration-300",
              conversation.status === 'connected'
                ? conversation.isSpeaking
                  ? "bg-green-400/50 animate-pulse"
                  : "bg-white/50 animate-pulse"
                : "bg-white/10 h-1",
            )}
            style={
              conversation.status === 'connected' && isClient
                ? {
                    height: conversation.isSpeaking
                      ? `${30 + Math.sin(Date.now() / 100 + i * 0.5) * 20}%`
                      : `${20 + (i % 8) * 10}%`,
                    animationDelay: `${i * 0.05}s`,
                  }
                : { height: '4px' }
            }
          />
        ))}
      </div>

      <div className="flex flex-col items-center text-xs text-white/70">
        {error ? (
          <div className="text-red-400 text-center max-w-xs">
            {error.split('\n').map((line, index) => (
              <p key={index} className={index > 0 ? 'mt-1' : ''}>{line}</p>
            ))}
          </div>
        ) : conversation.status === 'connected' ? (
          <>
            <p>Connected</p>
            <p>Agent is {conversation.isSpeaking ? 'speaking' : 'listening'}</p>
          </>
        ) : isConnecting ? (
          <p>Connecting...</p>
        ) : (
          <p>Click to start conversation</p>
        )}
      </div>
    </div>
  );
}