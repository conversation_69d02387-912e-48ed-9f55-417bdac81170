'use client';

import { useConversation } from '@elevenlabs/react';
import { useCallback, useState, useEffect } from 'react';
import { <PERSON><PERSON>, MicO<PERSON>, AlertCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ConversationProps {
  onMessage?: (message: string) => void;
  className?: string;
}

export function Conversation({ onMessage, className }: ConversationProps) {
  const [error, setError] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [time, setTime] = useState(0);
  const [isClient, setIsClient] = useState(false);
  
  const conversation = useConversation({
    onConnect: () => {
      console.log('Connected to ElevenLabs');
      setError(null);
      setIsConnecting(false);
    },
    onDisconnect: () => {
      console.log('Disconnected from ElevenLabs');
      setIsConnecting(false);
    },
    onMessage: (message) => {
      console.log('Message:', message);
      if (message.type === 'user_transcript' && message.text && onMessage) {
        onMessage(message.text);
      }
    },
    onError: (error) => {
      console.error('Conversation error:', error);
      setError(error.message || 'An error occurred');
      setIsConnecting(false);
    },
  });

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (conversation.status === 'connected') {
      setTime(0);
      intervalId = setInterval(() => {
        setTime((t) => t + 1);
      }, 1000);
    } else {
      setTime(0);
    }

    return () => clearInterval(intervalId);
  }, [conversation.status]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  };

  const getSignedUrl = async (): Promise<string> => {
    const response = await fetch("/api/get-signed-url");
    if (!response.ok) {
      throw new Error(`Failed to get signed url: ${response.statusText}`);
    }
    const { signedUrl } = await response.json();
    return signedUrl;
  };

  const startConversation = useCallback(async () => {
    try {
      setError(null);
      setIsConnecting(true);
      
      // Request microphone permission
      await navigator.mediaDevices.getUserMedia({ audio: true });

      // Get signed URL for authentication
      const signedUrl = await getSignedUrl();

      // Start the conversation with your agent
      await conversation.startSession({
        signedUrl,
      });

    } catch (error) {
      console.error('Failed to start conversation:', error);
      setError(error instanceof Error ? error.message : 'Failed to start conversation');
      setIsConnecting(false);
    }
  }, [conversation]);

  const stopConversation = useCallback(async () => {
    await conversation.endSession();
  }, [conversation]);

  return (
    <div className={cn("flex flex-col items-center gap-4", className)}>
      <button
        onClick={conversation.status === 'connected' ? stopConversation : startConversation}
        disabled={isConnecting}
        className={cn(
          "group w-16 h-16 rounded-xl flex items-center justify-center transition-all",
          conversation.status === 'connected' 
            ? "bg-red-500/20 hover:bg-red-500/30" 
            : "bg-white/10 hover:bg-white/20",
          error && "ring-2 ring-red-500",
          isConnecting && "opacity-50 cursor-not-allowed"
        )}
      >
        {isConnecting ? (
          <Loader2 className="w-6 h-6 text-white animate-spin" />
        ) : error ? (
          <AlertCircle className="w-6 h-6 text-red-500" />
        ) : conversation.status === 'connected' ? (
          <div className="relative">
            <MicOff className="w-6 h-6 text-white" />
            {conversation.isSpeaking && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse" />
            )}
          </div>
        ) : (
          <Mic className="w-6 h-6 text-white/70" />
        )}
      </button>

      {conversation.status === 'connected' && (
        <span className={cn(
          "font-mono text-sm transition-opacity duration-300 text-white/70"
        )}>
          {formatTime(time)}
        </span>
      )}

      <div className="h-4 w-64 flex items-center justify-center gap-0.5">
        {[...Array(48)].map((_, i) => (
          <div
            key={i}
            className={cn(
              "w-0.5 rounded-full transition-all duration-300",
              conversation.status === 'connected' 
                ? conversation.isSpeaking 
                  ? "bg-green-400/50 animate-pulse" 
                  : "bg-white/50 animate-pulse"
                : "bg-white/10 h-1",
            )}
            style={
              conversation.status === 'connected' && isClient
                ? {
                    height: conversation.isSpeaking 
                      ? `${30 + Math.sin(Date.now() / 100 + i * 0.5) * 20}%`
                      : `${20 + (i % 8) * 10}%`,
                    animationDelay: `${i * 0.05}s`,
                  }
                : { height: '4px' }
            }
          />
        ))}
      </div>

      <div className="flex flex-col items-center text-xs text-white/70">
        {error ? (
          <p className="text-red-400">{error}</p>
        ) : conversation.status === 'connected' ? (
          <>
            <p>Connected</p>
            <p>Agent is {conversation.isSpeaking ? 'speaking' : 'listening'}</p>
          </>
        ) : isConnecting ? (
          <p>Connecting...</p>
        ) : (
          <p>Click to start conversation</p>
        )}
      </div>
    </div>
  );
}