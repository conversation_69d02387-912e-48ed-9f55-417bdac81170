"use client";

import { useState } from "react";
import { useCopilotAction } from "@copilotkit/react-core";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";

export default function CopilotFeatures() {
  const [weatherData, setWeatherData] = useState<{ location: string; temperature: number; condition: string } | null>(null);
  
  // Define a CopilotKit action for getting weather information
  useCopilotAction({
    name: "getWeather",
    description: "Get the current weather for a location",
    parameters: [
      {
        name: "location",
        type: "string",
        description: "The city or location to get weather for",
        required: true,
      },
    ],
    handler: async ({ location }) => {
      // In a real app, you would fetch data from a weather API
      // This is a mock implementation
      const mockWeatherData = {
        location,
        temperature: Math.floor(Math.random() * 30) + 10, // Random temp between 10-40°C
        condition: ["Sunny", "Cloudy", "Rainy", "Partly Cloudy"][Math.floor(Math.random() * 4)],
      };
      
      setWeatherData(mockWeatherData);
      return mockWeatherData;
    },
    render: ({ result }) => {
      if (!result) return null;
      
      return (
        <Card className="w-full max-w-sm mx-auto my-4">
          <CardHeader>
            <CardTitle>Weather in {result.location}</CardTitle>
            <CardDescription>Current conditions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-3xl font-bold">{result.temperature}°C</div>
              <div className="text-lg">{result.condition}</div>
            </div>
          </CardContent>
        </Card>
      );
    },
  });

  // Define another action for voice commands
  useCopilotAction({
    name: "voiceCommand",
    description: "Execute a voice command for the assistant",
    parameters: [
      {
        name: "command",
        type: "string",
        description: "The voice command to execute",
        required: true,
      },
    ],
    handler: async ({ command }) => {
      // Process different voice commands
      const commandLower = command.toLowerCase();
      let response = "";
      
      if (commandLower.includes("hello") || commandLower.includes("hi")) {
        response = "Hello there! How can I help you today?";
      } else if (commandLower.includes("time")) {
        response = `The current time is ${new Date().toLocaleTimeString()}`;
      } else if (commandLower.includes("date")) {
        response = `Today is ${new Date().toLocaleDateString()}`;
      } else {
        response = `I received your command: "${command}", but I'm not sure how to process it.`;
      }
      
      return { command, response };
    },
    render: ({ result }) => {
      if (!result) return null;
      
      return (
        <Card className="w-full max-w-sm mx-auto my-4 bg-primary/10">
          <CardHeader>
            <CardTitle>Voice Command</CardTitle>
            <CardDescription>{result.command}</CardDescription>
          </CardHeader>
          <CardContent>
            <p>{result.response}</p>
          </CardContent>
        </Card>
      );
    },
  });

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold text-center mb-8">CopilotKit Features</h1>
      
      <Tabs defaultValue="instructions" className="max-w-2xl mx-auto">
        <TabsList className="grid grid-cols-3">
          <TabsTrigger value="instructions">Instructions</TabsTrigger>
          <TabsTrigger value="voiceDemo">Voice Features</TabsTrigger>
          <TabsTrigger value="weatherDemo">Weather Demo</TabsTrigger>
        </TabsList>
        
        <TabsContent value="instructions" className="p-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Using Voice Commands</CardTitle>
              <CardDescription>Try these example phrases with the Copilot sidebar</CardDescription>
            </CardHeader>
            <CardContent className="space-y-2">
              <ul className="list-disc pl-5 space-y-2">
                <li>"What's the weather in New York?"</li>
                <li>"Check the weather in London"</li>
                <li>"Hello" or "Hi there"</li>
                <li>"What time is it?"</li>
                <li>"What's today's date?"</li>
              </ul>
            </CardContent>
            <CardFooter>
              <p className="text-sm text-muted-foreground">The sidebar is available on all pages of the app.</p>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="voiceDemo" className="p-4 space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Voice-Enabled Copilot</CardTitle>
              <CardDescription>Talk to the assistant and hear responses</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-primary/10 p-4 rounded-lg border border-primary/20">
                <h3 className="font-medium text-lg mb-2">Voice Input Features</h3>
                <p className="mb-3">Click the microphone icon in the chat input to start/stop voice recording:</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Speech is converted to text as you speak</li>
                  <li>Recording stops automatically after you finish speaking</li>
                  <li>Your message is sent automatically when recording ends</li>
                </ul>
              </div>
              
              <div className="bg-primary/10 p-4 rounded-lg border border-primary/20">
                <h3 className="font-medium text-lg mb-2">Voice Output Features</h3>
                <p className="mb-3">The assistant will speak its responses out loud:</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Assistant responses are read aloud using text-to-speech</li>
                  <li>Click the speaker icon to mute/unmute the assistant voice</li>
                  <li>Voice output works on all pages of the application</li>
                </ul>
              </div>
            </CardContent>
            <CardFooter>
              <Button 
                onClick={() => {
                  // Open the sidebar if it's not already open
                  const sidebarButton = document.querySelector('[aria-label="Open Copilot"]');
                  if (sidebarButton) {
                    (sidebarButton as HTMLButtonElement).click();
                  }
                }}
                className="w-full"
              >
                Open Voice Assistant
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="weatherDemo" className="p-4">
          <Card>
            <CardHeader>
              <CardTitle>Weather Check</CardTitle>
              <CardDescription>Click the buttons to check weather in different cities</CardDescription>
            </CardHeader>
            <CardContent className="flex flex-wrap gap-2">
              {["New York", "London", "Tokyo", "Sydney", "Paris"].map((city) => (
                <Button 
                  key={city}
                  variant="outline" 
                  onClick={() => {
                    const mockWeatherData = {
                      location: city,
                      temperature: Math.floor(Math.random() * 30) + 10,
                      condition: ["Sunny", "Cloudy", "Rainy", "Partly Cloudy"][Math.floor(Math.random() * 4)],
                    };
                    setWeatherData(mockWeatherData);
                  }}
                >
                  {city}
                </Button>
              ))}
            </CardContent>
            
            {weatherData && (
              <CardFooter className="block">
                <Card className="w-full mt-4">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">Weather in {weatherData.location}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between">
                      <span className="text-2xl font-bold">{weatherData.temperature}°C</span>
                      <span>{weatherData.condition}</span>
                    </div>
                  </CardContent>
                </Card>
              </CardFooter>
            )}
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}