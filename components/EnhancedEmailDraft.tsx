"use client"

import { useState, useRef, useCallback } from "react"
import { 
  Bold, Italic, Underline, Link, List, ListOrdered, 
  Quote, Code, Paperclip, Clock, Flag, Send, X,
  ChevronDown, ChevronUp, Palette, Type, Image,
  Smile, AtSign, Hash, AlignLeft, AlignCenter, AlignRight,
  Undo, Redo, Save, FileText, Mail, Calendar
} from "lucide-react"
import { cn } from "@/lib/utils"

interface EmailDraftProps {
  to: string
  cc?: string
  bcc?: string
  subject: string
  body: string
  priority?: 'low' | 'normal' | 'high'
  scheduledTime?: Date
  attachments?: File[]
  signature?: string
  onChange: (field: string, value: any) => void
  onCancel: () => void
  onSend: () => void
  onSaveDraft?: () => void
  onSchedule?: (date: Date) => void
}

interface EmailTemplate {
  id: string
  name: string
  subject: string
  body: string
}

const EMAIL_TEMPLATES: EmailTemplate[] = [
  {
    id: "meeting",
    name: "Meeting Request",
    subject: "Meeting Request - [Topic]",
    body: `Dear [Name],

I hope this email finds you well. I would like to schedule a meeting to discuss [topic].

Would you be available for a [duration] meeting on [date] at [time]? If this doesn't work for you, please let me know your availability.

Looking forward to hearing from you.

Best regards,
[Your name]`
  },
  {
    id: "followup",
    name: "Follow-up",
    subject: "Following up on our conversation",
    body: `Hi [Name],

Thank you for taking the time to speak with me [timeframe]. I wanted to follow up on our discussion about [topic].

[Key points discussed]

[Next steps or action items]

Please let me know if you have any questions or if there's anything else I can help with.

Best regards,
[Your name]`
  },
  {
    id: "introduction",
    name: "Introduction",
    subject: "Introduction - [Your name]",
    body: `Dear [Name],

My name is [Your name] and I'm [your role/position]. [Person who introduced you] suggested I reach out to you regarding [reason].

[Brief background about yourself]

[Why you're reaching out]

I would love to connect and learn more about [their work/company]. Would you be open to a brief call or coffee chat?

Thank you for your time.

Best regards,
[Your name]`
  }
]

export default function EnhancedEmailDraft({
  to,
  cc = "",
  bcc = "",
  subject,
  body,
  priority = 'normal',
  scheduledTime,
  attachments = [],
  signature = "",
  onChange,
  onCancel,
  onSend,
  onSaveDraft,
  onSchedule
}: EmailDraftProps) {
  const [showCcBcc, setShowCcBcc] = useState(false)
  const [showScheduler, setShowScheduler] = useState(false)
  const [showTemplates, setShowTemplates] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<string>("")
  const [showFormatting, setShowFormatting] = useState(true)
  const [wordCount, setWordCount] = useState(body.split(/\s+/).filter(word => word.length > 0).length)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const editorRef = useRef<HTMLDivElement>(null)

  // Format toolbar actions
  const formatText = useCallback((command: string, value?: string) => {
    document.execCommand(command, false, value)
    if (editorRef.current) {
      onChange("body", editorRef.current.innerHTML)
    }
  }, [onChange])

  const handleEditorChange = useCallback(() => {
    if (editorRef.current) {
      const text = editorRef.current.innerText
      const words = text.split(/\s+/).filter(word => word.length > 0).length
      setWordCount(words)
      onChange("body", editorRef.current.innerHTML)
    }
  }, [onChange])

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    onChange("attachments", [...attachments, ...files])
  }

  const removeAttachment = (index: number) => {
    const newAttachments = attachments.filter((_, i) => i !== index)
    onChange("attachments", newAttachments)
  }

  const applyTemplate = (template: EmailTemplate) => {
    onChange("subject", template.subject)
    onChange("body", template.body)
    setSelectedTemplate(template.id)
    setShowTemplates(false)
  }

  const getPriorityColor = () => {
    switch (priority) {
      case 'high': return 'text-red-500 border-red-500'
      case 'low': return 'text-blue-500 border-blue-500'
      default: return 'text-gray-500 border-gray-500'
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-zinc-900 rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden border border-zinc-700">
        {/* Header */}
        <div className="bg-zinc-800 px-6 py-4 border-b border-zinc-700 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Mail className="w-5 h-5 text-zinc-400" />
            <h2 className="text-xl font-semibold text-zinc-100">Compose Email</h2>
            <div className="flex items-center gap-2 ml-4">
              <button
                onClick={() => setShowTemplates(!showTemplates)}
                className="text-sm px-3 py-1 rounded-md bg-zinc-700 text-zinc-300 hover:bg-zinc-600 transition-colors"
              >
                <FileText className="w-4 h-4 inline mr-1" />
                Templates
              </button>
              {onSaveDraft && (
                <button
                  onClick={onSaveDraft}
                  className="text-sm px-3 py-1 rounded-md bg-zinc-700 text-zinc-300 hover:bg-zinc-600 transition-colors"
                >
                  <Save className="w-4 h-4 inline mr-1" />
                  Save Draft
                </button>
              )}
            </div>
          </div>
          <button
            onClick={onCancel}
            className="p-2 hover:bg-zinc-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-zinc-400" />
          </button>
        </div>

        {/* Templates Dropdown */}
        {showTemplates && (
          <div className="absolute top-16 left-6 bg-zinc-800 border border-zinc-700 rounded-lg shadow-lg z-10 w-64">
            <div className="p-2">
              <p className="text-xs text-zinc-500 uppercase tracking-wider mb-2">Email Templates</p>
              {EMAIL_TEMPLATES.map(template => (
                <button
                  key={template.id}
                  onClick={() => applyTemplate(template)}
                  className={cn(
                    "w-full text-left px-3 py-2 rounded-md hover:bg-zinc-700 transition-colors",
                    selectedTemplate === template.id && "bg-zinc-700"
                  )}
                >
                  <p className="text-sm font-medium text-zinc-200">{template.name}</p>
                  <p className="text-xs text-zinc-500 truncate">{template.subject}</p>
                </button>
              ))}
            </div>
          </div>
        )}

        <div className="overflow-y-auto max-h-[calc(90vh-80px)]">
          {/* Recipients Section */}
          <div className="px-6 py-4 space-y-3 border-b border-zinc-700/50">
            {/* To Field */}
            <div className="flex items-center gap-3">
              <label className="text-sm font-medium text-zinc-400 w-16">To:</label>
              <div className="flex-1 relative">
                <AtSign className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-zinc-500" />
                <input
                  type="email"
                  value={to}
                  onChange={(e) => onChange("to", e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full pl-10 pr-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-zinc-100 placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <button
                onClick={() => setShowCcBcc(!showCcBcc)}
                className="text-sm text-zinc-400 hover:text-zinc-200 transition-colors"
              >
                {showCcBcc ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                <span className="ml-1">{showCcBcc ? 'Hide' : 'CC/BCC'}</span>
              </button>
            </div>

            {/* CC/BCC Fields */}
            {showCcBcc && (
              <>
                <div className="flex items-center gap-3">
                  <label className="text-sm font-medium text-zinc-400 w-16">CC:</label>
                  <input
                    type="email"
                    value={cc}
                    onChange={(e) => onChange("cc", e.target.value)}
                    placeholder="<EMAIL>"
                    className="flex-1 px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-zinc-100 placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div className="flex items-center gap-3">
                  <label className="text-sm font-medium text-zinc-400 w-16">BCC:</label>
                  <input
                    type="email"
                    value={bcc}
                    onChange={(e) => onChange("bcc", e.target.value)}
                    placeholder="<EMAIL>"
                    className="flex-1 px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-zinc-100 placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </>
            )}

            {/* Subject */}
            <div className="flex items-center gap-3">
              <label className="text-sm font-medium text-zinc-400 w-16">Subject:</label>
              <input
                type="text"
                value={subject}
                onChange={(e) => onChange("subject", e.target.value)}
                placeholder="Email subject"
                className="flex-1 px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-zinc-100 placeholder:text-zinc-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Formatting Toolbar */}
          {showFormatting && (
            <div className="px-6 py-3 border-b border-zinc-700/50 flex items-center gap-1 flex-wrap">
              <div className="flex items-center gap-1 mr-3">
                <button
                  onClick={() => formatText('bold')}
                  className="p-2 hover:bg-zinc-700 rounded transition-colors"
                  title="Bold"
                >
                  <Bold className="w-4 h-4 text-zinc-400" />
                </button>
                <button
                  onClick={() => formatText('italic')}
                  className="p-2 hover:bg-zinc-700 rounded transition-colors"
                  title="Italic"
                >
                  <Italic className="w-4 h-4 text-zinc-400" />
                </button>
                <button
                  onClick={() => formatText('underline')}
                  className="p-2 hover:bg-zinc-700 rounded transition-colors"
                  title="Underline"
                >
                  <Underline className="w-4 h-4 text-zinc-400" />
                </button>
              </div>

              <div className="w-px h-6 bg-zinc-700 mx-1" />

              <div className="flex items-center gap-1 mr-3">
                <button
                  onClick={() => formatText('insertUnorderedList')}
                  className="p-2 hover:bg-zinc-700 rounded transition-colors"
                  title="Bullet List"
                >
                  <List className="w-4 h-4 text-zinc-400" />
                </button>
                <button
                  onClick={() => formatText('insertOrderedList')}
                  className="p-2 hover:bg-zinc-700 rounded transition-colors"
                  title="Numbered List"
                >
                  <ListOrdered className="w-4 h-4 text-zinc-400" />
                </button>
              </div>

              <div className="w-px h-6 bg-zinc-700 mx-1" />

              <div className="flex items-center gap-1 mr-3">
                <button
                  onClick={() => formatText('justifyLeft')}
                  className="p-2 hover:bg-zinc-700 rounded transition-colors"
                  title="Align Left"
                >
                  <AlignLeft className="w-4 h-4 text-zinc-400" />
                </button>
                <button
                  onClick={() => formatText('justifyCenter')}
                  className="p-2 hover:bg-zinc-700 rounded transition-colors"
                  title="Align Center"
                >
                  <AlignCenter className="w-4 h-4 text-zinc-400" />
                </button>
                <button
                  onClick={() => formatText('justifyRight')}
                  className="p-2 hover:bg-zinc-700 rounded transition-colors"
                  title="Align Right"
                >
                  <AlignRight className="w-4 h-4 text-zinc-400" />
                </button>
              </div>

              <div className="w-px h-6 bg-zinc-700 mx-1" />

              <div className="flex items-center gap-1">
                <button
                  onClick={() => {
                    const url = prompt('Enter URL:')
                    if (url) formatText('createLink', url)
                  }}
                  className="p-2 hover:bg-zinc-700 rounded transition-colors"
                  title="Insert Link"
                >
                  <Link className="w-4 h-4 text-zinc-400" />
                </button>
                <button
                  onClick={() => formatText('undo')}
                  className="p-2 hover:bg-zinc-700 rounded transition-colors"
                  title="Undo"
                >
                  <Undo className="w-4 h-4 text-zinc-400" />
                </button>
                <button
                  onClick={() => formatText('redo')}
                  className="p-2 hover:bg-zinc-700 rounded transition-colors"
                  title="Redo"
                >
                  <Redo className="w-4 h-4 text-zinc-400" />
                </button>
              </div>
            </div>
          )}

          {/* Email Body */}
          <div className="px-6 py-4">
            <div
              ref={editorRef}
              contentEditable
              onInput={handleEditorChange}
              dangerouslySetInnerHTML={{ __html: body }}
              className="min-h-[300px] max-h-[400px] overflow-y-auto p-4 bg-zinc-800 border border-zinc-700 rounded-lg text-zinc-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent prose prose-invert max-w-none"
              style={{ lineHeight: '1.6' }}
              suppressContentEditableWarning={true}
            />
            <div className="mt-2 flex items-center justify-between text-xs text-zinc-500">
              <span>{wordCount} words</span>
              <button
                onClick={() => setShowFormatting(!showFormatting)}
                className="hover:text-zinc-300 transition-colors"
              >
                {showFormatting ? 'Hide' : 'Show'} formatting
              </button>
            </div>
          </div>

          {/* Attachments */}
          {attachments.length > 0 && (
            <div className="px-6 py-3 border-t border-zinc-700/50">
              <p className="text-sm font-medium text-zinc-400 mb-2">Attachments</p>
              <div className="space-y-2">
                {attachments.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-zinc-800 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Paperclip className="w-4 h-4 text-zinc-500" />
                      <span className="text-sm text-zinc-300">{file.name}</span>
                      <span className="text-xs text-zinc-500">
                        ({(file.size / 1024 / 1024).toFixed(2)} MB)
                      </span>
                    </div>
                    <button
                      onClick={() => removeAttachment(index)}
                      className="p-1 hover:bg-zinc-700 rounded transition-colors"
                    >
                      <X className="w-4 h-4 text-zinc-500" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Signature */}
          {signature && (
            <div className="px-6 py-3 border-t border-zinc-700/50">
              <div className="text-sm text-zinc-400 whitespace-pre-wrap">{signature}</div>
            </div>
          )}
        </div>

        {/* Footer Actions */}
        <div className="px-6 py-4 bg-zinc-800 border-t border-zinc-700 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <input
              ref={fileInputRef}
              type="file"
              multiple
              onChange={handleFileSelect}
              className="hidden"
            />
            <button
              onClick={() => fileInputRef.current?.click()}
              className="p-2 hover:bg-zinc-700 rounded-lg transition-colors"
              title="Attach files"
            >
              <Paperclip className="w-5 h-5 text-zinc-400" />
            </button>
            
            <button
              onClick={() => setShowScheduler(!showScheduler)}
              className="p-2 hover:bg-zinc-700 rounded-lg transition-colors"
              title="Schedule send"
            >
              <Clock className="w-5 h-5 text-zinc-400" />
            </button>

            <div className="relative">
              <button
                onClick={() => {
                  const newPriority = priority === 'normal' ? 'high' : priority === 'high' ? 'low' : 'normal'
                  onChange('priority', newPriority)
                }}
                className={cn(
                  "p-2 hover:bg-zinc-700 rounded-lg transition-colors border",
                  getPriorityColor()
                )}
                title="Set priority"
              >
                <Flag className="w-5 h-5" />
              </button>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <button
              onClick={onCancel}
              className="px-4 py-2 text-zinc-300 hover:text-zinc-100 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={onSend}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center gap-2"
            >
              <Send className="w-4 h-4" />
              Send
            </button>
          </div>
        </div>

        {/* Schedule Modal */}
        {showScheduler && onSchedule && (
          <div className="absolute bottom-20 right-6 bg-zinc-800 border border-zinc-700 rounded-lg shadow-lg p-4 w-64">
            <p className="text-sm font-medium text-zinc-200 mb-3">Schedule Email</p>
            <input
              type="datetime-local"
              onChange={(e) => {
                onSchedule(new Date(e.target.value))
                setShowScheduler(false)
              }}
              className="w-full px-3 py-2 bg-zinc-700 border border-zinc-600 rounded-lg text-zinc-100 text-sm"
              min={new Date().toISOString().slice(0, 16)}
            />
          </div>
        )}
      </div>
    </div>
  )
}