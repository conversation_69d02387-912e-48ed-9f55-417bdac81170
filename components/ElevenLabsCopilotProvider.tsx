"use client";

import "@copilotkit/react-ui/styles.css";
import { useState, useEffect, useRef, ReactNode } from "react";
import { CopilotKit, useCopilotChat } from "@copilotkit/react-core";
import { CopilotSidebar, CopilotChatInput } from "@copilotkit/react-ui";
import { Mic, Square, Volume2, VolumeX } from "lucide-react";
import { v4 as uuidv4 } from "uuid";
import { useConversation } from "@11labs/react";
import { toast, Toaster } from "sonner";

interface ElevenLabsCopilotProviderProps {
  children: ReactNode;
  enableSidebar?: boolean;
  sidebarProps?: {
    defaultOpen?: boolean;
    clickOutsideToClose?: boolean;
    labels?: {
      title?: string;
      initial?: string;
    };
  };
}

export default function ElevenLabsCopilotProvider({
  children,
  enableSidebar = true,
  sidebarProps = {
    defaultOpen: false,
    clickOutsideToClose: true,
    labels: {
      title: "Voice Assistant",
      initial: "👋 Hi there! How can I help you today?"
    }
  }
}: ElevenLabsCopilotProviderProps) {
  // CopilotKit public API key
  const publicApiKey = "ck_pub_4adc457c723a7b00b6fd291a2fc96bf0";
  // Theme color for CopilotKit UI elements
  const themeColor = "#6366f1";
  
  return (
    <CopilotKit
      publicApiKey={publicApiKey}
    >
      <div style={{ "--copilot-kit-primary-color": themeColor } as any}>
        {children}
        
        {enableSidebar && (
          <>
            <Toaster position="top-center" />
            <CopilotSidebar
              defaultOpen={sidebarProps.defaultOpen}
              clickOutsideToClose={sidebarProps.clickOutsideToClose}
              labels={{
                title: sidebarProps.labels?.title || "Voice Assistant",
                initial: sidebarProps.labels?.initial || "👋 Hi there! How can I help you today?"
              }}
              chatInput={<ElevenLabsChatInput />}
            />
          </>
        )}
      </div>
    </CopilotKit>
  );
}

function ElevenLabsChatInput() {
  const { append, messages } = useCopilotChat();
  const [isListening, setIsListening] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [initialized, setInitialized] = useState(false);
  const lastMessageRef = useRef<string | null>(null);
  const elevenlabsSessionIdRef = useRef<string>(uuidv4());
  
  // Initialize ElevenLabs conversation
  const conversation = useConversation({
    onError: (error: string) => {
      console.error("ElevenLabs error:", error);
      toast.error(error);
      setIsListening(false);
    },
    onConnect: () => {
      toast.success("Connected to voice assistant");
      setInitialized(true);
    },
    onMessage: ({ message, source }: { message: string; source: "ai" | "user" }) => {
      if (source === "user") {
        // When ElevenLabs recognizes user speech, send it to CopilotKit
        append({
          content: message,
          role: "user"
        });
      } else if (source === "ai" && !isMuted) {
        // ElevenLabs will handle speaking the AI response
        // We just need to ensure we don't duplicate the message
        lastMessageRef.current = message;
      }
    }
  });

  // Connect to ElevenLabs API
  const connectToElevenLabs = async () => {
    if (conversation.status === "connected") return;
    
    toast("Connecting to voice assistant…");
    
    try {
      // Ensure browser supports MediaDevices API (secure context or localhost)
      if (
        typeof navigator === "undefined" ||
        !navigator.mediaDevices ||
        typeof navigator.mediaDevices.getUserMedia !== "function"
      ) {
        toast.error("Your browser does not support microphone access");
        return;
      }
      
      await navigator.mediaDevices.getUserMedia({ audio: true });
      
      const res = await fetch("/api/i", { method: "POST" });
      const data = await res.json();
      
      if (data?.error) throw new Error(data.error);
      
      // Start ElevenLabs session with the signed URL
      await conversation.startSession({ signedUrl: data.apiKey });
      setIsListening(true);
    } catch (err: any) {
      console.error("ElevenLabs startSession error", err);
      
      let errorMessage = "Failed to connect to voice assistant";
      if (err?.message) {
        errorMessage += `: ${err.message}`;
      }
      
      toast.error(errorMessage);
      setIsListening(false);
    }
  };
  
  // Disconnect from ElevenLabs API
  const disconnectFromElevenLabs = async () => {
    if (conversation.status !== "connected") return;
    try {
      await conversation.endSession();
      setIsListening(false);
    } catch (err) {
      console.error("ElevenLabs endSession error", err);
    }
  };
  
  // Initialize ElevenLabs connection on first load
  useEffect(() => {
    if (!initialized) {
      connectToElevenLabs();
    }
    
    // Cleanup on unmount
    return () => {
      disconnectFromElevenLabs();
    };
  }, [initialized]);
  
  // Monitor CopilotKit messages for assistant responses
  useEffect(() => {
    // Get the last assistant message
    const lastAssistantMsg = [...messages].reverse().find(m => m.role === "assistant");
    
    if (lastAssistantMsg && 
        lastAssistantMsg.content && 
        lastMessageRef.current !== lastAssistantMsg.content &&
        !isMuted && 
        conversation.status === "connected") {
      
      // Send the new message to ElevenLabs
      if (typeof window !== 'undefined') {
        // Use the ElevenLabs clientTools interface to send the message
        const messageEvent = new CustomEvent('elevenlabs-message', { 
          detail: { text: lastAssistantMsg.content } 
        });
        window.dispatchEvent(messageEvent);
        
        // Update the last message ref
        lastMessageRef.current = lastAssistantMsg.content;
      }
    }
  }, [messages, isMuted, conversation.status]);
  
  const toggleListening = () => {
    if (isListening) {
      disconnectFromElevenLabs();
    } else {
      connectToElevenLabs();
    }
  };
  
  const toggleMute = () => {
    setIsMuted(!isMuted);
  };
  
  return (
    <div className="relative">
      {/* Standard text input */}
      <CopilotChatInput 
        className="pr-24" 
        placeholder={isListening ? "Listening... speak now" : "Type a message or click the mic to speak..."} 
      />
      
      {/* Voice controls */}
      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
        {/* Voice output toggle */}
        <button
          onClick={toggleMute}
          className={`p-2 rounded-full transition-colors ${isMuted ? 'bg-gray-200 text-gray-600' : 'bg-indigo-100 text-indigo-600'}`}
          title={isMuted ? "Unmute assistant" : "Mute assistant"}
          aria-label={isMuted ? "Unmute assistant" : "Mute assistant"}
        >
          {isMuted ? <VolumeX size={18} /> : <Volume2 size={18} />}
        </button>
        
        {/* Voice input button */}
        <button
          onClick={toggleListening}
          className={`p-2 rounded-full transition-colors ${isListening ? 'bg-red-500 text-white' : 'bg-indigo-500 text-white'}`}
          title={isListening ? "Stop recording" : "Start recording"}
          aria-label={isListening ? "Stop recording" : "Start recording"}
        >
          {isListening ? <Square size={18} /> : <Mic size={18} />}
        </button>
      </div>
      
      {/* Status indicator for ElevenLabs connection */}
      <div className="absolute bottom-full left-0 mb-2 text-xs">
        {conversation.status === "connecting" && (
          <span className="bg-amber-100 text-amber-800 px-2 py-1 rounded-md">Connecting...</span>
        )}
        {isListening && (
          <span className="bg-green-100 text-green-800 px-2 py-1 rounded-md">Listening</span>
        )}
      </div>
    </div>
  );
}