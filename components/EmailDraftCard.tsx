export default function EmailDraftCard({ to, subject, body, onChange, onCancel, onSend }: {
  to: string
  subject: string
  body: string
  onChange: (field: "to" | "subject" | "body", value: string) => void
  onCancel: () => void
  onSend: () => void
}) {
  return (
    <div className="max-w-md mx-auto mb-4">
      {/* Card wrapper */}
      <div className="rounded-lg border border-blue-500 bg-zinc-800 text-zinc-100 shadow-sm">
        <div className="flex flex-col space-y-2 p-4">
          <h2 className="text-xl font-semibold">Draft Email</h2>
        </div>
        <div className="p-4 space-y-4">
          <div>
            <label htmlFor="email-to" className="text-sm font-medium text-zinc-200">To</label>
            <input
              id="email-to"
              type="email"
              value={to}
              onChange={(e) => onChange("to", e.target.value)}
              placeholder="Recipient email"
              className="mt-1 block w-full rounded-md border border-zinc-600 bg-zinc-700 px-3 py-2 text-base placeholder:text-zinc-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label htmlFor="email-subject" className="text-sm font-medium text-zinc-200">Subject</label>
            <input
              id="email-subject"
              value={subject}
              onChange={(e) => onChange("subject", e.target.value)}
              placeholder="Subject"
              className="mt-1 block w-full rounded-md border border-zinc-600 bg-zinc-700 px-3 py-2 text-base placeholder:text-zinc-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label htmlFor="email-body" className="text-sm font-medium text-zinc-200">Body</label>
            <textarea
              id="email-body"
              value={body}
              onChange={(e) => onChange("body", e.target.value)}
              placeholder="Write your email here..."
              className="mt-1 block w-full min-h-[100px] rounded-md border border-zinc-600 bg-zinc-700 px-3 py-2 text-base placeholder:text-zinc-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
        <div className="flex items-center p-4 pt-0 space-x-2 justify-end">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 rounded-md border border-zinc-600 bg-transparent text-zinc-200 hover:border-zinc-500"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={onSend}
            className="px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-700"
          >
            Send
          </button>
        </div>
      </div>
    </div>
  )
} 