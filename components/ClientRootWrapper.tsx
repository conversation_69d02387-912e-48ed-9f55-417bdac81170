"use client";

import { ReactNode } from "react";
import dynamic from "next/dynamic";

// Dynamic import with SSR disabled for client components
const ElevenLabsCopilotProvider = dynamic(() => import('./ElevenLabsCopilotProvider'), { ssr: false });

export default function ClientRootWrapper({ children }: { children: ReactNode }) {
  return (
    <ElevenLabsCopilotProvider 
      enableSidebar={true}
      sidebarProps={{
        defaultOpen: false,
        clickOutsideToClose: true,
        labels: {
          title: "ElevenLabs Voice Assistant",
          initial: "👋 Hi! I'm your ElevenLabs-powered voice assistant. You can speak to me by clicking the microphone icon, and I'll speak my responses with ElevenLabs high-quality voice. Click the speaker icon to mute/unmute my voice."
        }
      }}
    >
      {children}
    </ElevenLabsCopilotProvider>
  );
}