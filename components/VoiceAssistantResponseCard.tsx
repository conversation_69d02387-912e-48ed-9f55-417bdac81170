"use client"

import { <PERSON><PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"

interface VoiceAssistantResponseCardProps {
  content: string
  timestamp?: string
  isActive?: boolean
}

export default function VoiceAssistantResponseCard({
  content,
  timestamp,
  isActive = false,
}: VoiceAssistantResponseCardProps) {
  return (
    <div
      className={cn(
        "group relative overflow-hidden",
        "bg-zinc-800/95 backdrop-blur-xl",
        "border border-zinc-700/50",
        "rounded-2xl transition-all duration-300",
        isActive ? "border-blue-500/70" : "hover:border-blue-700/50",
        isActive ? "shadow-[0_0_30px_-5px_rgba(59,130,246,0.5)]" : "hover:shadow-[0_0_30px_-5px_rgba(59,130,246,0.3)]",
      )}
    >
      <div
        className={cn(
          "absolute inset-0 -z-10 transition-opacity duration-300",
          isActive ? "opacity-100" : "opacity-0 group-hover:opacity-100",
          "bg-gradient-to-br from-transparent via-blue-900/10 to-blue-900/20",
        )}
      />

      <div className="p-4 border-b border-zinc-700/50">
        <div className="flex items-center gap-3">
          <div
            className={cn(
              "w-10 h-10 rounded-xl",
              "transition-colors duration-300",
              "flex items-center justify-center",
              isActive ? "bg-blue-900/50" : "bg-zinc-700/50 group-hover:bg-blue-900/30",
            )}
          >
            <Bot className={cn("w-5 h-5", isActive ? "text-blue-300" : "text-blue-400")} />
          </div>
          <p className="text-base font-medium text-zinc-200">Ask ARA</p>
          {isActive && (
            <span className="ml-auto px-2 py-1 text-xs font-medium rounded-md bg-blue-900/30 text-blue-300 animate-pulse">
              Active
            </span>
          )}
        </div>
      </div>

      <div className="p-4 space-y-3">
        <p className="text-sm text-zinc-300 leading-relaxed whitespace-pre-line">{content}</p>
      </div>

      <div className={cn("px-4 py-2 border-t border-zinc-700/50 flex justify-end", !timestamp && "hidden")}>
        <p className="text-xs text-zinc-500">{timestamp || ""}</p>
      </div>
    </div>
  )
}
