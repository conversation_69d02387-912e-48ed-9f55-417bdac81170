"use client";
import React from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

interface Field {
  id: string;
  type: "text" | "email" | "select" | "checkbox" | "textarea";
  label: string;
  options?: string[];
  required?: boolean;
}
interface Props {
  title: string;
  fields: Field[];
  submitLabel?: string;
}

export default function DynamicForm({ title, fields, submitLabel = "Submit" }: Props) {
  const schemaShape: Record<string, z.ZodTypeAny> = {};
  fields.forEach((f) => {
    let zodType: z.ZodTypeAny = z.string();
    if (f.type === "email") zodType = z.string().email();
    if (f.required) zodType = zodType.min(1, { message: `${f.label} is required` });
    schemaShape[f.id] = zodType;
  });

  const schema = z.object(schemaShape);
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({ resolver: zodResolver(schema) });

  function onSubmit(values: any) {
    console.log("dynamic form submit", values);
    // TODO: send to server / tool invocation
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="border rounded-md p-4 space-y-3">
      <h3 className="font-medium text-lg">{title}</h3>
      {fields.map((f) => (
        <div key={f.id} className="space-y-1">
          <label className="block text-sm font-medium" htmlFor={f.id}>
            {f.label}
          </label>
          {f.type === "select" ? (
            <select
              id={f.id}
              {...register(f.id)}
              className="border rounded p-2 w-full"
            >
              {f.options?.map((opt) => (
                <option key={opt} value={opt}>
                  {opt}
                </option>
              ))}
            </select>
          ) : f.type === "textarea" ? (
            <textarea
              id={f.id}
              {...register(f.id)}
              className="border rounded p-2 w-full min-h-[80px]"
            />
          ) : (
            <input
              id={f.id}
              type={f.type}
              {...register(f.id)}
              className="border rounded p-2 w-full"
            />
          )}
          {errors[f.id]?.message && (
            <p className="text-xs text-red-600">{String(errors[f.id]?.message)}</p>
          )}
        </div>
      ))}
      <button
        type="submit"
        className="px-4 py-2 rounded-md bg-black text-white hover:opacity-90"
      >
        {submitLabel}
      </button>
    </form>
  );
}
