"use client"

import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"
import Image from "next/image"
import g8Icon from "@/g8.svg"
import g8DarkIcon from "@/Frame 2.svg"

interface AIInput08Props {
  isActive: boolean
  onClick: () => void
  isDemo?: boolean
}

export default function AIInput_08({ isActive, onClick, isDemo = false }: AIInput08Props) {
  const [submitted, setSubmitted] = useState(false)
  // Use useRef instead of useState to track client-side rendering
  const isClientRef = useRef(false)
  const [isDemoRunning, setIsDemoRunning] = useState(false)  // Start with false, update in useEffect
  const [isHovered, setIsHovered] = useState(false)

  // Circular bars configuration
  // Bars animation removed – replaced by Ask ARA logo SVG

  // Handle client-side initialization
  useEffect(() => {
    isClientRef.current = true
    // Only set demo state after client-side hydration
    setIsDemoRunning(isDemo)
  }, [])

  // Update submitted state based on isActive prop
  useEffect(() => {
    setSubmitted(isActive)
  }, [isActive])

  /**
   * Remove that, only used for demo
   */
  useEffect(() => {
    // Skip this effect during server rendering and initial hydration
    if (!isClientRef.current) return
    if (!isDemoRunning) return

    let timeoutId: NodeJS.Timeout
    const runAnimation = () => {
      setSubmitted(true)
      timeoutId = setTimeout(() => {
        setSubmitted(false)
        timeoutId = setTimeout(runAnimation, 1000)
      }, 3000)
    }

    // Slight delay to ensure hydration is complete
    const initialTimeout = setTimeout(runAnimation, 100)
    return () => {
      clearTimeout(timeoutId)
      clearTimeout(initialTimeout)
    }
  }, [isDemoRunning])

  const handleClick = () => {
    if (isDemoRunning) {
      setIsDemoRunning(false)
      setSubmitted(false)
    } else {
      onClick()
    }
  }

  // Update the iconVariants to make the hover state the default inactive state

  // Animation variants for the icon
  const iconVariants = {
    inactive: {
      scale: 0.75, // Changed from 0.5 to 0.75 (same as hover)
      opacity: 0.9, // Changed from 0.8 to 0.9 (same as hover)
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 25,
      },
    },
    active: {
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 25,
      },
    },
    hover: {
      scale: 0.75,
      opacity: 0.9,
      transition: {
        type: "spring",
        stiffness: 500,
        damping: 25,
      },
    },
  }

  // Determine which variant to use
  const currentVariant = isActive ? "active" : isHovered ? "hover" : "inactive"

  return (
    <>
      <style>
        {`
@keyframes circular-bars {
  0% {
      opacity: 1;
      rotate: 0deg;
      scale: 1;
  }
  70% {
      opacity: 0;
      rotate: -40deg;
      scale: 0.9;
  }
  100% {
      opacity: 1;
      rotate: 0deg;
      scale: 1;
  }
}

.glassmorphism {
  background: rgba(17, 25, 40, 0.65);
  backdrop-filter: blur(16px) saturate(180%);
  -webkit-backdrop-filter: blur(16px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.125);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.45);
}

.glassmorphism:hover {
  background: rgba(17, 25, 40, 0.75);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.55);
}

.glassmorphism:active, .glassmorphism.active {
  background: rgba(17, 25, 40, 0.75);
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.5);
}

.glass-highlight {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.1) 100%);
}

.glass-inner-shadow {
  box-shadow: inset 0 1px 1px 0 rgba(255, 255, 255, 0.15), 
              inset 0 -1px 1px 0 rgba(0, 0, 0, 0.15);
}

/* Removed logoPulse keyframes (glow disabled) */
`}
      </style>
      <div className="relative">
        {/* Glow disabled */}

        {/* Also update the button styling to include hover styles by default */}
        <button
          className={cn(
            "relative w-16 h-16 rounded-full flex items-center justify-center transition-all duration-500",
            submitted ? "border border-zinc-500/60" : "border border-zinc-500/30 hover:border-zinc-500/60",
            "bg-transparent backdrop-blur-sm",
          )}

          type="button"
          onClick={handleClick}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          style={{
            transform: `perspective(1000px) ${submitted ? "translateZ(5px) scale(0.98)" : "translateZ(0) scale(1)"}`,
            transition: "transform 0.3s ease, box-shadow 0.3s ease, background 0.3s ease",
          }}
        >
          {/* Faint inner highlight */}
          <div className="absolute inset-0 rounded-full glass-highlight opacity-50 pointer-events-none" />

          {/* Content container with motion */}
          <motion.div
            className="relative flex items-center justify-center z-10"
            variants={iconVariants}
            animate={currentVariant}
            initial="inactive"
            style={{ width: "100%", height: "100%" }}
          >
            <Image
              src={submitted ? g8DarkIcon : g8Icon}
              alt="Ask ARA"
              width={64}
              height={64}
              className="w-full h-full object-contain"
              priority
            />
            <span className="sr-only">Assistant toggle</span>
          </motion.div>

          {/* Bottom reflection */}
          <div className="absolute bottom-0 left-0 right-0 h-1/3 rounded-b-2xl bg-gradient-to-t from-white/5 to-transparent pointer-events-none" />
        </button>
      </div>
    </>
  )
}

