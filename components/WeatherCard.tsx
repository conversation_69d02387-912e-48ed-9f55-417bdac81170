"use client"

import { useState, useEffect } from "react"
import { CloudRain, Thermometer, Wind, Compass, Clock } from "lucide-react"
import { cn } from "@/lib/utils"
import { format } from "date-fns"

interface WeatherCardProps {
  city: string
  temperature?: number
  condition?: string
  humidity?: number
  windSpeed?: number
  windDirection?: string
  fetchedAt?: string
  error?: string
}

export default function WeatherCard({ 
  city, 
  temperature,
  condition = "Loading...",
  humidity,
  windSpeed,
  windDirection,
  fetchedAt,
  error
}: WeatherCardProps) {
  const [data, setData] = useState<Omit<WeatherCardProps, 'city'> | null>(null)
  const [isLoading, setIsLoading] = useState(!temperature)
  const [isExpanded, setIsExpanded] = useState(false)
  
  useEffect(() => {
    // If we already have temperature data, use it
    if (temperature) {
      setData({
        temperature,
        condition,
        humidity,
        windSpeed,
        windDirection,
        fetchedAt: fetchedAt || format(new Date(), "h:mm a 'on' MMM d, yyyy")
      })
      setIsLoading(false)
      return
    }
    
    // Otherwise fetch the weather data
    async function fetchWeather() {
      try {
        setIsLoading(true)
        // This would typically call an API endpoint
        // For now we'll simulate with mock data
        const mockData = {
          temperature: Math.round(15 + Math.random() * 15),
          condition: ["Sunny", "Partly Cloudy", "Rainy", "Overcast"][Math.floor(Math.random() * 4)],
          humidity: Math.round(40 + Math.random() * 40),
          windSpeed: Math.round(5 + Math.random() * 15),
          windDirection: ["N", "NE", "E", "SE", "S", "SW", "W", "NW"][Math.floor(Math.random() * 8)],
          fetchedAt: format(new Date(), "h:mm a 'on' MMM d, yyyy")
        }
        
        // Simulate network delay
        setTimeout(() => {
          setData(mockData)
          setIsLoading(false)
        }, 1500)
      } catch (err) {
        setData({ error: "Failed to fetch weather data" })
        setIsLoading(false)
      }
    }
    
    fetchWeather()
  }, [city, temperature, condition, humidity, windSpeed, windDirection, fetchedAt])

  return (
    <div
      className={cn(
        "group relative overflow-hidden",
        "bg-zinc-900/95 backdrop-blur-xl",
        "border border-zinc-800/50",
        "rounded-2xl transition-all duration-300",
        "hover:border-zinc-700/50",
        "hover:shadow-[0_0_30px_-5px_rgba(96,165,250,0.3)]",
      )}
    >
      <div
        className="absolute inset-0 -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300
        bg-gradient-to-br from-transparent via-blue-900/10 to-blue-900/20"
      />

      <div className="p-4 border-b border-zinc-800/50">
        <div className="flex items-center gap-3">
          <div
            className="w-10 h-10 rounded-xl bg-zinc-800/50 group-hover:bg-blue-900/30
            transition-colors duration-300 flex items-center justify-center"
          >
            <CloudRain className="w-5 h-5 text-blue-400" />
          </div>
          <p className="text-base font-medium text-zinc-200">
            Weather for {city}
          </p>
        </div>
      </div>

      <div className="p-4 space-y-3">
        {isLoading ? (
          <div className="flex flex-col items-center py-6">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-2" />
            <p className="text-sm text-zinc-400">Fetching weather...</p>
          </div>
        ) : data?.error ? (
          <p className="text-sm text-red-400">{data.error}</p>
        ) : (
          <>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Thermometer className="w-5 h-5 text-blue-400 mr-2" />
                <span className="text-xl font-semibold text-zinc-200">
                  {data?.temperature}°C
                </span>
              </div>
              <span className="text-zinc-300">{data?.condition}</span>
            </div>
            
            <button 
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-sm text-blue-400 hover:text-blue-300 transition-colors duration-300 mt-2"
            >
              {isExpanded ? "Show less" : "Show details"}
            </button>
            
            {isExpanded && (
              <div className="mt-4 space-y-3 pt-3 border-t border-zinc-800/50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="text-zinc-400 text-sm">Humidity</span>
                  </div>
                  <span className="text-zinc-300 text-sm">{data?.humidity}%</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className="text-zinc-400 text-sm">Wind</span>
                  </div>
                  <span className="text-zinc-300 text-sm">
                    {data?.windSpeed} km/h {data?.windDirection}
                  </span>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      <div className="px-4 py-2 border-t border-zinc-800/50 flex justify-between items-center">
        <div className="flex gap-2">
          <span className="px-2 py-1 text-xs font-medium rounded-md
            bg-zinc-800/50 text-zinc-300 transition-colors duration-300
            group-hover:bg-blue-900/30 group-hover:text-blue-300">
            #weather
          </span>
        </div>
        <div className="flex items-center text-xs text-zinc-500">
          <Clock className="w-3.5 h-3.5 mr-1" />
          {data?.fetchedAt || "Just now"}
        </div>
      </div>
    </div>
  )
}
