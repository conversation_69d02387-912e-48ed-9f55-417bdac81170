"use client"

import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { CardType, validateCard } from "@/lib/cardSchemas"
import CardRenderer from "@/components/CardRenderer"
import { Conversation } from "@/components/conversation";

interface VoiceInterfaceProps {
  isOpen: boolean
  onClose: () => void
  onMessage?: (message: string) => void // Add optional onMessage prop
}

interface AssistantMessage {
  id: string;
  role: "user" | "assistant";
  content?: string; // text
  cardType?: CardType;
  payload?: any;
}

export default function VoiceInterface({ isOpen, onClose, onMessage }: VoiceInterfaceProps) {
  const [messages, setMessages] = useState<AssistantMessage[]>([])

  const handleMessage = (message: string) => {
    if (onMessage) {
      onMessage(message)
    }
    setMessages((prevMessages) => [
      ...prevMessages,
      {
        id: Date.now().toString(),
        role: "user",
        content: message,
      },
    ])
  }


  return (
    <div
      className={cn(
        "fixed inset-x-0 bottom-0 bg-zinc-900 transition-transform duration-300 ease-in-out transform",
        isOpen ? "translate-y-0" : "translate-y-full",
      )}
    >
      <div className="w-full py-4">
        <div className="relative max-w-xl w-full mx-auto flex items-center flex-col gap-4">
          <Conversation onMessage={handleMessage} />

          {messages.map((m) => (
            <div key={m.id} className="my-2">
              {m.cardType ? (
                <CardRenderer type={m.cardType} payload={m.payload} />
              ) : (
                <p className={m.role === "assistant" ? "text-gray-900" : "text-gray-600"}>{m.content}</p>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
