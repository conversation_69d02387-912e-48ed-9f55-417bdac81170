"use client"

import { useState, useEffect, type HTMLAttributes, type ReactNode, type CSSProperties } from "react"
import { cn } from "@/lib/utils"
import { Globe, ChevronDown, ChevronUp } from "lucide-react"

// Define glow colors for different icon types
const GLOW_COLORS = {
  stats: "emerald",
  user: "blue",
  location: "violet",
  time: "amber",
  trend: "indigo",
} as const

type GlowColor = keyof typeof GLOW_COLORS

interface CardProps extends HTMLAttributes<HTMLDivElement> {
  data: {
    question: string
    answer: string
    source: string
    icon: ReactNode
    status: string
    tags: string[]
    timestamp?: string
    type?: GlowColor
    sourceData?: string
  }
}

export default function VoiceAssistantCard({ data, className, ...props }: CardProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [displayTimestamp, setDisplayTimestamp] = useState(data.timestamp || "")
  const glowColor = data.type ? GLOW_COLORS[data.type] : "blue"
  
  // Set "Just now" on the client side only
  useEffect(() => {
    setDisplayTimestamp(data.timestamp || "Just now")
  }, [data.timestamp])

  return (
    <div
      className={cn(
        "group relative overflow-hidden",
        "bg-zinc-900/95 backdrop-blur-xl",
        "border border-zinc-800/50",
        "rounded-2xl transition-all duration-300",
        "hover:border-zinc-700/50",
        `hover:shadow-[0_0_30px_-5px_var(--glow-color)] hover:shadow-${glowColor}-500/20`,
        `active:shadow-[0_0_20px_-3px_var(--glow-color)] active:shadow-${glowColor}-500/30`,
        className,
      )}
      style={
        {
          "--glow-color": `rgb(var(--${glowColor}-500))`,
        } as CSSProperties
      }
      {...props}
    >
      <div
        className={cn(
          "absolute inset-0 -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300",
          "bg-gradient-to-br from-transparent",
          `via-${glowColor}-900/10`,
          `to-${glowColor}-900/20`,
        )}
      />

      <div className="p-4 border-b border-zinc-800/50">
        <div className="flex items-center gap-3">
          <div
            className={cn(
              "w-10 h-10 rounded-xl",
              "bg-zinc-800/50",
              `group-hover:bg-${glowColor}-900/30`,
              "transition-colors duration-300",
              "flex items-center justify-center",
            )}
          >
            {data.icon}
          </div>
          <p className="text-base font-medium text-zinc-200 line-clamp-2">{data.question}</p>
        </div>
      </div>

      <div className="p-4 space-y-3">
        <p className="text-sm text-zinc-300 leading-relaxed">{data.answer}</p>
      </div>

      <div className="px-4 py-2 border-t border-zinc-800/50 flex justify-between items-center">
        <div className="flex gap-2">
          {data.tags?.map((tag, index) => (
            <span
              key={index}
              className={cn(
                "px-2 py-1 text-xs font-medium rounded-md",
                "bg-zinc-800/50 text-zinc-300",
                "transition-colors duration-300",
                `group-hover:bg-${glowColor}-900/30`,
                `group-hover:text-${glowColor}-300`,
              )}
            >
              #{tag}
            </span>
          ))}
        </div>
        <p className="text-xs text-zinc-500">{displayTimestamp}</p>
      </div>

      <div className="p-4 border-t border-zinc-800/50">
        <div className="flex items-center justify-between">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center gap-2 text-xs text-zinc-400 hover:text-zinc-300 transition-colors"
          >
            <Globe className="w-3.5 h-3.5" />
            <span>Source: {data.source}</span>
          </button>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 rounded-full hover:bg-zinc-800/50 transition-colors"
          >
            {isExpanded ? (
              <ChevronUp className="w-3.5 h-3.5 text-zinc-400" />
            ) : (
              <ChevronDown className="w-3.5 h-3.5 text-zinc-400" />
            )}
          </button>
        </div>
      </div>

      {isExpanded && (
        <div className="p-4 border-t border-zinc-800/50 bg-zinc-800/30">
          <h4 className="text-sm font-medium text-zinc-200 mb-2">Source Data:</h4>
          <pre className="text-xs text-zinc-300 whitespace-pre-wrap break-words">
            {data.sourceData || "No additional source data available."}
          </pre>
        </div>
      )}
    </div>
  )
}

