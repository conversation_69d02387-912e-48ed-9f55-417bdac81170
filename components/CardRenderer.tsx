"use client";
import React, { useState } from "react";
import { CardType, validateCard } from "@/lib/cardSchemas";
import { Progress } from "@/components/ui/progress";
import { Checkbox } from "@/components/ui/checkbox";
import DynamicForm from "@/components/forms/DynamicForm";

interface Props {
  type: CardType;
  payload: any;
  /** fired when a confirmation card is accepted */
  onConfirm?: () => void;
  onCancel?: () => void;
}

export default function CardRenderer({ type, payload, onConfirm, onCancel }: Props) {
  const validation = validateCard(type, payload);
  if (!validation.success) {
    return (
      <div className="border border-red-500 p-2 text-red-600 text-sm rounded-md">
        Invalid card payload: {validation.error}
      </div>
    );
  }
  const data = validation.data as any;

  switch (type) {
    case "InfoCard":
      return (
        <div className="rounded-md border p-4 bg-blue-50 dark:bg-blue-900/20">
          <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
            {data.title}
          </h3>
          <p className="text-sm text-blue-800 dark:text-blue-200">{data.content}</p>
        </div>
      );

    case "SuccessAlert":
      return (
        <div className="border-l-4 border-green-600 p-4 bg-green-50 dark:bg-green-900/20 rounded-md">
          <h3 className="font-medium text-green-900 dark:text-green-100 mb-1">
            {data.title}
          </h3>
          <p className="text-sm text-green-800 dark:text-green-200">{data.message}</p>
        </div>
      );

    case "ErrorAlert":
      return (
        <div className="border-l-4 border-red-600 p-4 bg-red-50 dark:bg-red-900/20 rounded-md">
          <h3 className="font-medium text-red-900 dark:text-red-100 mb-1">{data.title}</h3>
          <p className="text-sm text-red-800 dark:text-red-200">{data.message}</p>
        </div>
      );

    case "ConfirmationCard":
      return (
        <div className="border rounded-md p-4 bg-yellow-50 dark:bg-yellow-900/20 space-y-2">
          <h3 className="font-medium text-yellow-900 dark:text-yellow-100">
            {data.title}
          </h3>
          <p className="text-sm text-yellow-800 dark:text-yellow-200">
            {data.question}
          </p>
          <div className="flex gap-2">
            <button
              className="px-3 py-1 rounded-md bg-red-600 text-white text-sm"
              onClick={onCancel}
            >
              {data.cancelLabel}
            </button>
            <button
              className="px-3 py-1 rounded-md bg-green-600 text-white text-sm"
              onClick={onConfirm}
            >
              {data.confirmLabel}
            </button>
          </div>
        </div>
      );

    case "ProductCard":
      return (
        <div className="border rounded-md p-4 flex gap-4">
          <img
            src={data.image}
            alt={data.title}
            className="w-24 h-24 object-cover rounded-md"
          />
          <div className="flex-1">
            <h3 className="font-medium mb-1">{data.title}</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
              {data.currency} {data.price}
            </p>
            {data.rating && (
              <p className="text-xs text-gray-500">Rating: {data.rating}/5</p>
            )}
          </div>
        </div>
      );

    case "WorkflowStepCard":
      return (
        <div className="border rounded-md p-4 space-y-2">
          <h3 className="font-medium">
            {data.title} ({data.step}/{data.totalSteps})
          </h3>
          {data.description && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {data.description}
            </p>
          )}
          {typeof data.progress === "number" && (
            <Progress value={Math.round(data.progress * 100)} />
          )}
        </div>
      );

    case "DynamicFormCard":
      return <DynamicForm title={data.title} fields={data.fields} submitLabel={data.submitLabel} />;

    case "SummaryCard":
      return (
        <div className="border rounded-md p-4 text-xs">
          <h3 className="font-medium mb-2">{data.title}</h3>
          <pre className="whitespace-pre-wrap">{JSON.stringify(data, null, 2)}</pre>
        </div>
      );

    case "MediaCard":
      return (
        <div className="border rounded-md p-4 space-y-2">
          <img
            src={data.thumbnail}
            alt={data.title}
            className="w-full h-40 object-cover rounded-md"
          />
          <div>
            <h3 className="font-medium">{data.title}</h3>
            {data.duration && (
              <p className="text-xs text-gray-500">{data.duration}</p>
            )}
            <a
              href={data.mediaUrl}
              target="_blank"
              rel="noreferrer"
              className="text-sm text-blue-600 hover:underline"
            >
              Play / Download
            </a>
          </div>
        </div>
      );

    case "TaskCard":
      return (
        <div className="border rounded-md p-4 space-y-2">
          <h3 className="font-medium mb-2">{data.title}</h3>
          <ul className="space-y-1">
            {data.tasks.map((t: any, idx: number) => (
              <li key={idx} className="flex items-center gap-2 text-sm">
                <Checkbox defaultChecked={t.done} />
                <span>{t.label}</span>
              </li>
            ))}
          </ul>
        </div>
      );

    case "EventCard":
      return (
        <div className="border rounded-md p-4 space-y-1">
          <h3 className="font-medium text-lg">{data.title}</h3>
          <p className="text-sm text-gray-600">
            {data.date} – {data.time}
          </p>
          <p className="text-sm text-gray-600">{data.location}</p>
          {data.description && (
            <p className="text-xs text-gray-500">{data.description}</p>
          )}
        </div>
      );

    case "ProgressCard":
      return (
        <div className="border rounded-md p-4 space-y-2">
          <h3 className="font-medium mb-1">{data.title}</h3>
          {typeof data.percent === "number" && (
            <Progress value={Math.round(data.percent * 100)} />
          )}
          <p className="text-xs">Status: {data.status}</p>
          {data.log && (
            <pre className="text-xs bg-black/5 dark:bg-white/10 p-2 rounded-md max-h-40 overflow-auto">
              {data.log}
            </pre>
          )}
        </div>
      );

    default:
      return (
        <pre className="text-xs whitespace-pre-wrap">
          {JSON.stringify(data, null, 2)}
        </pre>
      );
  }
}
