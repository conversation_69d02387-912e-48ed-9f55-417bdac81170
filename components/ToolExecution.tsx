"use client"

import { useState } from "react"
import { ChevronRight, CheckCircle, XCircle, Loader2, Wren<PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"

interface ToolExecution {
  id: string
  name: string
  status: 'executing' | 'complete' | 'error'
  args?: any
  result?: any
  error?: string
  timestamp: Date
}

interface ToolExecutionProps {
  executions: ToolExecution[]
  className?: string
}

export default function ToolExecution({ executions, className }: ToolExecutionProps) {
  const [expandedTools, setExpandedTools] = useState<Set<string>>(new Set())

  const toggleExpanded = (id: string) => {
    setExpandedTools(prev => {
      const next = new Set(prev)
      if (next.has(id)) {
        next.delete(id)
      } else {
        next.add(id)
      }
      return next
    })
  }

  if (executions.length === 0) return null

  return (
    <div className={cn("fixed bottom-20 right-4 max-w-md w-full space-y-2 z-40", className)}>
      <div className="bg-zinc-800/90 backdrop-blur-sm rounded-lg border border-zinc-700 p-3 max-h-96 overflow-y-auto">
        <div className="flex items-center gap-2 mb-3">
          <Wrench className="w-4 h-4 text-zinc-400" />
          <h3 className="text-sm font-medium text-zinc-200">Tool Executions</h3>
        </div>
        
        <div className="space-y-2">
          {executions.slice(-5).reverse().map((exec) => {
            const isExpanded = expandedTools.has(exec.id)
            
            return (
              <div
                key={exec.id}
                className="bg-zinc-900/50 rounded-md border border-zinc-700/50 overflow-hidden"
              >
                <button
                  onClick={() => toggleExpanded(exec.id)}
                  className="w-full p-2 flex items-center gap-2 hover:bg-zinc-800/50 transition-colors"
                >
                  <ChevronRight
                    className={cn(
                      "w-3 h-3 text-zinc-500 transition-transform",
                      isExpanded && "rotate-90"
                    )}
                  />
                  
                  {exec.status === 'executing' && (
                    <Loader2 className="w-3 h-3 text-blue-400 animate-spin" />
                  )}
                  {exec.status === 'complete' && (
                    <CheckCircle className="w-3 h-3 text-green-400" />
                  )}
                  {exec.status === 'error' && (
                    <XCircle className="w-3 h-3 text-red-400" />
                  )}
                  
                  <span className="text-xs font-mono text-zinc-300 flex-1 text-left">
                    {exec.name}
                  </span>
                  
                  <span className="text-[10px] text-zinc-500">
                    {exec.timestamp.toLocaleTimeString()}
                  </span>
                </button>
                
                {isExpanded && (
                  <div className="px-3 pb-2 space-y-2 border-t border-zinc-700/50">
                    {exec.args && (
                      <div className="mt-2">
                        <p className="text-[10px] text-zinc-500 uppercase tracking-wider mb-1">Arguments</p>
                        <pre className="text-[10px] text-zinc-400 bg-zinc-950/50 p-2 rounded overflow-x-auto">
                          {JSON.stringify(exec.args, null, 2)}
                        </pre>
                      </div>
                    )}
                    
                    {exec.status === 'complete' && exec.result && (
                      <div>
                        <p className="text-[10px] text-zinc-500 uppercase tracking-wider mb-1">Result</p>
                        <pre className="text-[10px] text-green-400 bg-zinc-950/50 p-2 rounded overflow-x-auto">
                          {typeof exec.result === 'string' ? exec.result : JSON.stringify(exec.result, null, 2)}
                        </pre>
                      </div>
                    )}
                    
                    {exec.status === 'error' && exec.error && (
                      <div>
                        <p className="text-[10px] text-zinc-500 uppercase tracking-wider mb-1">Error</p>
                        <pre className="text-[10px] text-red-400 bg-zinc-950/50 p-2 rounded overflow-x-auto">
                          {exec.error}
                        </pre>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )
          })}
        </div>
        
        {executions.length > 5 && (
          <p className="text-[10px] text-zinc-500 text-center mt-2">
            Showing last 5 of {executions.length} executions
          </p>
        )}
      </div>
    </div>
  )
}