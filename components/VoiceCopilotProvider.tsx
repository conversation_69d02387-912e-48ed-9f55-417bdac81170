"use client";

import "@copilotkit/react-ui/styles.css";
import { useState, useEffect, useRef, ReactNode } from "react";
import { CopilotKit, useCopilotChat } from "@copilotkit/react-core";
import { CopilotSidebar, CopilotChatInput } from "@copilotkit/react-ui";
import { Mic, Square, Volume2, VolumeX } from "lucide-react";
import { v4 as uuidv4 } from "uuid";

interface VoiceCopilotProviderProps {
  children: ReactNode;
  enableSidebar?: boolean;
  sidebarProps?: {
    defaultOpen?: boolean;
    clickOutsideToClose?: boolean;
    labels?: {
      title?: string;
      initial?: string;
    };
  };
}

export default function VoiceCopilotProvider({
  children,
  enableSidebar = true,
  sidebarProps = {
    defaultOpen: false,
    clickOutsideToClose: true,
    labels: {
      title: "Voice Assistant",
      initial: "👋 Hi there! How can I help you today?"
    }
  }
}: VoiceCopilotProviderProps) {
  // CopilotKit public API key
  const publicApiKey = "ck_pub_4adc457c723a7b00b6fd291a2fc96bf0";
  // Theme color for CopilotKit UI elements
  const themeColor = "#6366f1";
  
  return (
    <CopilotKit
      publicApiKey={publicApiKey}
    >
      <div style={{ "--copilot-kit-primary-color": themeColor } as any}>
        {children}
        
        {enableSidebar && (
          <CopilotSidebar
            defaultOpen={sidebarProps.defaultOpen}
            clickOutsideToClose={sidebarProps.clickOutsideToClose}
            labels={{
              title: sidebarProps.labels?.title || "Voice Assistant",
              initial: sidebarProps.labels?.initial || "👋 Hi there! How can I help you today?"
            }}
            chatInput={<VoiceChatInput />}
          />
        )}
      </div>
    </CopilotKit>
  );
}

function VoiceChatInput() {
  const { append } = useCopilotChat();
  const [recording, setRecording] = useState(false);
  const [transcript, setTranscript] = useState("");
  const [speaking, setSpeaking] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const synthRef = useRef<SpeechSynthesis | null>(null);
  const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);
  
  // Initialize speech recognition and synthesis
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Initialize speech recognition
      if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognitionRef.current = new SpeechRecognition();
        recognitionRef.current.continuous = true;
        recognitionRef.current.interimResults = true;
        
        recognitionRef.current.onresult = (event) => {
          const result = event.results[event.results.length - 1];
          const transcript = result[0].transcript;
          setTranscript(transcript);
          
          // If the result is final, submit it
          if (result.isFinal) {
            setRecording(false);
            submitTranscript(transcript);
          }
        };
        
        recognitionRef.current.onerror = (event) => {
          console.error('Speech recognition error:', event.error);
          setRecording(false);
        };
      }
      
      // Initialize speech synthesis
      if ('speechSynthesis' in window) {
        synthRef.current = window.speechSynthesis;
      }
    }
    
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.abort();
      }
      if (synthRef.current && utteranceRef.current) {
        synthRef.current.cancel();
      }
    };
  }, []);
  
  // Monitor messages for assistant responses and speak them
  useEffect(() => {
    const copilotMessages = document.querySelectorAll('[data-message-role="assistant"]');
    const lastMessage = copilotMessages[copilotMessages.length - 1];
    
    if (lastMessage && !isMuted) {
      // Get the text content of the last message
      const messageText = lastMessage.textContent || "";
      
      // Only speak if we have a new message and we're not already speaking
      if (messageText && !speaking && synthRef.current) {
        speakText(messageText);
      }
    }
  }, [isMuted, speaking]);
  
  // Periodically check for new messages (since we don't have direct access to the copilot chat state)
  useEffect(() => {
    const checkInterval = setInterval(() => {
      const copilotMessages = document.querySelectorAll('[data-message-role="assistant"]');
      const lastMessage = copilotMessages[copilotMessages.length - 1];
      
      if (lastMessage && !isMuted && !speaking && synthRef.current) {
        // Get the text content of the last message
        const messageText = lastMessage.textContent || "";
        
        // Check if this is a new message by comparing to data attribute
        const lastSpokenId = lastMessage.getAttribute('data-spoken');
        const messageId = lastMessage.getAttribute('data-message-id') || uuidv4();
        
        if (messageText && !lastSpokenId) {
          speakText(messageText);
          // Mark as spoken
          lastMessage.setAttribute('data-spoken', 'true');
          lastMessage.setAttribute('data-message-id', messageId);
        }
      }
    }, 1000);
    
    return () => clearInterval(checkInterval);
  }, [isMuted, speaking]);
  
  const speakText = (text: string) => {
    if (!synthRef.current || isMuted) return;
    
    setSpeaking(true);
    
    utteranceRef.current = new SpeechSynthesisUtterance(text);
    utteranceRef.current.rate = 1.0;
    utteranceRef.current.pitch = 1.0;
    utteranceRef.current.volume = 1.0;
    
    utteranceRef.current.onend = () => {
      setSpeaking(false);
    };
    
    utteranceRef.current.onerror = (event) => {
      console.error('Speech synthesis error:', event);
      setSpeaking(false);
    };
    
    synthRef.current.speak(utteranceRef.current);
  };
  
  const toggleRecording = () => {
    if (recording) {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      setRecording(false);
    } else {
      setTranscript("");
      if (recognitionRef.current) {
        recognitionRef.current.start();
      }
      setRecording(true);
    }
  };
  
  const toggleMute = () => {
    setIsMuted(!isMuted);
    
    // If currently speaking, stop it
    if (speaking && synthRef.current) {
      synthRef.current.cancel();
      setSpeaking(false);
    }
  };
  
  const submitTranscript = (text: string) => {
    if (text.trim()) {
      append({
        content: text.trim(),
        role: "user"
      });
      setTranscript("");
    }
  };
  
  return (
    <div className="relative">
      {/* Standard text input */}
      <CopilotChatInput className="pr-24" placeholder={recording ? "Listening..." : "Type a message or click the mic to speak..."} />
      
      {/* Voice controls */}
      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
        {/* Voice output toggle */}
        <button
          onClick={toggleMute}
          className={`p-2 rounded-full transition-colors ${isMuted ? 'bg-gray-200 text-gray-600' : 'bg-indigo-100 text-indigo-600'}`}
          title={isMuted ? "Unmute assistant" : "Mute assistant"}
          aria-label={isMuted ? "Unmute assistant" : "Mute assistant"}
        >
          {isMuted ? <VolumeX size={18} /> : <Volume2 size={18} />}
        </button>
        
        {/* Voice input button */}
        <button
          onClick={toggleRecording}
          className={`p-2 rounded-full transition-colors ${recording ? 'bg-red-500 text-white' : 'bg-indigo-500 text-white'}`}
          title={recording ? "Stop recording" : "Start recording"}
          aria-label={recording ? "Stop recording" : "Start recording"}
        >
          {recording ? <Square size={18} /> : <Mic size={18} />}
        </button>
      </div>
      
      {/* Transcript preview */}
      {recording && transcript && (
        <div className="absolute left-0 right-0 bottom-full mb-2 bg-white dark:bg-gray-800 p-2 rounded shadow-lg border border-gray-200 dark:border-gray-700 text-sm">
          {transcript}
        </div>
      )}
    </div>
  );
}

// Add TypeScript declarations for Web Speech API
declare global {
  interface Window {
    SpeechRecognition: typeof SpeechRecognition;
    webkitSpeechRecognition: typeof SpeechRecognition;
  }
}