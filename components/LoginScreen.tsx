"use client"

import Auth08 from "@/components/Auth08"

interface LoginScreenProps {
  onLogin: (email: string, password: string) => void
}

export default function LoginScreen({ onLogin }: LoginScreenProps) {
  return (
    <div className="flex items-center justify-center min-h-screen bg-zinc-100 dark:bg-zinc-900">
      <Auth08 showGithub={true} showGoogle={true} className="transform scale-90 sm:scale-100" onSubmit={onLogin} />
    </div>
  )
}

