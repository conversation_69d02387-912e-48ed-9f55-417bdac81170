"use client"

import { useState } from "react"
import { Utensils, ExternalLink, ChevronDown, ChevronUp, CheckCircle, XCircle, AlertCircle } from "lucide-react"
import { cn } from "@/lib/utils"

interface InspectionRatingCardProps {
  suburb: string
  rating?: string | number
  establishmentName?: string
  inspectionDate?: string
  sourceUrl?: string
  sourceData?: string
  status?: 'passed' | 'failed' | 'warning' | 'loading'
}

export default function InspectionRatingCard({
  suburb,
  rating,
  establishmentName = "Recent establishments",
  inspectionDate,
  sourceUrl = "#",
  sourceData,
  status = "loading"
}: InspectionRatingCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  
  const getStatusIcon = () => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="w-5 h-5 text-emerald-400" />
      case 'failed': 
        return <XCircle className="w-5 h-5 text-red-400" />
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-amber-400" />
      case 'loading':
      default:
        return (
          <div className="w-5 h-5 rounded-full border-2 border-violet-400 border-t-transparent animate-spin" />
        )
    }
  }
  
  const getRatingLabel = () => {
    if (status === 'loading') return 'Loading...'
    if (!rating) return 'No rating available'
    
    if (typeof rating === 'number' || !isNaN(Number(rating))) {
      const numRating = Number(rating)
      if (numRating >= 90) return `${rating} - Excellent`
      if (numRating >= 70) return `${rating} - Good`
      if (numRating >= 50) return `${rating} - Fair`
      return `${rating} - Poor`
    }
    
    return rating
  }

  return (
    <div
      className={cn(
        "group relative overflow-hidden",
        "bg-zinc-900/95 backdrop-blur-xl",
        "border border-zinc-800/50",
        "rounded-2xl transition-all duration-300",
        "hover:border-zinc-700/50",
        "hover:shadow-[0_0_30px_-5px_rgba(167,139,250,0.3)]",
      )}
    >
      <div
        className="absolute inset-0 -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300
        bg-gradient-to-br from-transparent via-violet-900/10 to-violet-900/20"
      />

      <div className="p-4 border-b border-zinc-800/50">
        <div className="flex items-center gap-3">
          <div
            className="w-10 h-10 rounded-xl bg-zinc-800/50 group-hover:bg-violet-900/30
            transition-colors duration-300 flex items-center justify-center"
          >
            <Utensils className="w-5 h-5 text-violet-400" />
          </div>
          <p className="text-base font-medium text-zinc-200">
            Inspection Ratings for {suburb}
          </p>
        </div>
      </div>

      <div className="p-4 space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <span className="text-zinc-200 font-medium">{establishmentName}</span>
          </div>
          <span className="text-zinc-300">{getRatingLabel()}</span>
        </div>
        
        {inspectionDate && (
          <p className="text-sm text-zinc-400">
            Inspection date: {inspectionDate}
          </p>
        )}
        
        <button 
          onClick={() => setIsExpanded(!isExpanded)}
          className="text-sm text-violet-400 hover:text-violet-300 transition-colors duration-300 mt-2 flex items-center gap-1"
        >
          {isExpanded ? "Hide details" : "Show details"}
          {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
        </button>
      </div>

      {isExpanded && sourceData && (
        <div className="p-4 border-t border-zinc-800/50 bg-zinc-800/30">
          <h4 className="text-sm font-medium text-zinc-200 mb-2">Inspection Details:</h4>
          <pre className="text-xs text-zinc-300 whitespace-pre-wrap break-words">
            {sourceData}
          </pre>
        </div>
      )}

      <div className="px-4 py-2 border-t border-zinc-800/50 flex justify-between items-center">
        <div className="flex gap-2">
          <span className="px-2 py-1 text-xs font-medium rounded-md
            bg-zinc-800/50 text-zinc-300 transition-colors duration-300
            group-hover:bg-violet-900/30 group-hover:text-violet-300">
            #inspection
          </span>
        </div>
        <a
          href={sourceUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center text-xs text-zinc-400 hover:text-violet-300 transition-colors"
        >
          Source <ExternalLink className="w-3.5 h-3.5 ml-1" />
        </a>
      </div>
    </div>
  )
}
