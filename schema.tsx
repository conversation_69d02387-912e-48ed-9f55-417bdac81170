import { neon, neonConfig } from "@neondatabase/serverless"
import "dotenv/config"

// Configure neon to use fetch for pooling
;(neonConfig as any).poolQueryViaFetch = true
// Add a reasonable timeout
;(neonConfig as any).fetchTimeout = 10000
// Add backoff for rate limiting
;(neonConfig as any).fetchRetry = 3
;(neonConfig as any).fetchRetryMaxDelay = 2000

const createMessagesTable = async () => {
  if (!process.env.DATABASE_URL) {
    console.error("DATABASE_URL environment variable not found.")
    process.exit(1)
  }

  try {
    const sql = neon(process.env.DATABASE_URL)

    // Check if table exists using a simpler query
    const tableExistsResult = (await sql.query(
      "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)",
      ["messages"],
    )) as any

    const exists = tableExistsResult.rows?.[0]?.exists === true

    if (!exists) {
      // Create the messages table with separate statements to avoid template literal issues
      await sql.query(
        "CREATE TABLE IF NOT EXISTS messages (created_at INTEGER, id TEXT PRIMARY KEY, session_id TEXT NOT NULL, content_type TEXT, content_transcript TEXT, object TEXT, role TEXT NOT NULL, status TEXT, type TEXT)",
      )

      // Create an index for faster queries
      await sql.query("CREATE INDEX IF NOT EXISTS idx_session_created_at ON messages (session_id, created_at)")
    }

    console.log("Setup schema successfully.")
  } catch (error) {
    console.error("Error setting up schema:", error)
    console.log("Failed to set up schema.")
    process.exit(1)
  }
}

createMessagesTable()
