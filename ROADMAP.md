# Production Roadmap & Checklist

> Tick the box (`[x]`) as each task is completed.  Sub-tasks may be added or refined during implementation.

---

## Architecture Diagram

```mermaid
flowchart LR
  subgraph Frontend (Next.js 15 / RSC)
    direction TB
    Workbench[/Workbench Page/]
    Mock1[IPhoneMockup\nagent-a]:::phone
    Mock2[IPhoneMockup\nagent-b]:::phone
    Mock3[IPhoneMockup\nagent-c]:::phone
    Mock4[IPhoneMockup\nagent-d]:::phone
    Sidebar[Settings & Provider Registry]
    Workbench --> Mock1 & Mock2 & Mock3 & Mock4
    Workbench -- Zustand/WebSocket --> Sidebar
  end

  subgraph Voice-Agent Workers (Fly machines)
    AgentA(livekit_agent.py pipeline A)
    AgentB(pipeline B)
  end

  subgraph LiveKit Cloud
    LKRoom((Room))
  end

  subgraph Infra
    Postgres[(Neon DB)]
    RedisCache[(Upstash Redis)]
    OTLP[(OTel Collector)]
    S3[(Audio storage)]
  end

  Mock1 -- WebRTC --> LKRoom
  Mock2 -- WebRTC --> LKRoom
  Mock3 -- WebRTC --> <PERSON>K<PERSON>oom
  Mock4 -- WebRTC --> LKRoom
  AgentA -- gRPC --> LKRoom
  AgentB -- gRPC --> LKRoom
  Frontend --- REST/WS ---> Postgres
  AgentA --- REST ---> Postgres
  Frontend --> OTLP
  AgentA --> OTLP
  classDef phone fill:#111,stroke:#555,color:#fff;
```

---

## Phase 0 — Stability & Tooling

- [ ] **Pin dependency versions** (`package.json`, `requirements-agents.txt`)
- [ ] **Add linting & formatting**  
  - [ ] ESLint (Next.js preset)  
  - [ ] Prettier + `prettier-plugin-tailwindcss`
- [ ] **Type Safety** – `tsc --noEmit` in CI
- [ ] **Vitest + testing-library/react**
- [ ] **GitHub Actions**: `lint`, `type-check`, `test`, Docker build
- [ ] **Tailwind purge optimisation**

## Phase 1 — Workbench MVP

- [ ] `/workbench` RSC page
  - [ ] Responsive 2×2 grid of `IPhoneMockup`s
  - [ ] Per-mockup `VoiceInterface` bound to `agentId`
- [ ] **Zustand store** for global UI state
- [ ] **Settings Sidebar** (Radix Drawer)
  - [ ] Agents list & CRUD
  - [ ] Provider registry (STT/TTS/LLM)
  - [ ] System prompt textarea

## Phase 2 — Backend & Persistence

- [ ] **Prisma schema** (`Agent`, `Provider`, `Conversation`)
- [ ] **API routes** `/api/agents` (CRUD)
  - [ ] Zod validation middleware
  - [ ] Auth middleware (next-auth / clerk)
- [ ] **WebSocket channel** for live transcripts (LiveKit or Next.js WS route)

## Phase 3 — Agent Flexibility & Deploy

- [ ] Refactor `livekit_agent.py` to env-driven pipeline
- [ ] Retry / circuit-breaker logic (tenacity)
- [ ] Add metrics (Prometheus client)
- [ ] **Docker multi-stage builds**
- [ ] Deploy:  
  - [ ] Next.js → Vercel  
  - [ ] Workers → Fly.io Machines
- [ ] Edge middleware: rate-limit, Helmet security headers

## Phase 4 — Testing & Release

- [ ] Playwright E2E tests (login, voice interaction)
- [ ] Blue/Green deploy scripts
- [ ] Health-check route & smoke tests
- [ ] Documentation & runbooks (`docs/`)

---

## Legend

- `[ ]` – Pending
- `[x]` – Completed
- `[-]` – Not applicable / dropped
