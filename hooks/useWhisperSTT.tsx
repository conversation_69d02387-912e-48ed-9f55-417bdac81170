"use client"

import { useEffect, useRef, useState, useCallback } from "react"

interface WhisperResult {
  text: string
  error: string | null
  isRecording: boolean
}

/**
 * Hook that uses MediaRecorder to capture audio and sends it to the Whisper API
 * for transcription via our server endpoint.
 * 
 * @param active Whether recording should be active
 * @returns Object containing transcription results and state
 */
export default function useWhisperSTT(active: boolean): WhisperResult {
  const [text, setText] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [isRecording, setIsRecording] = useState(false)
  
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const chunksRef = useRef<Blob[]>([])

  const sendAudioForTranscription = useCallback(async (audioBlob: Blob) => {
    try {
      const formData = new FormData()
      formData.append('audio', audioBlob, 'audio.webm')
      
      const response = await fetch('/api/transcribe', {
        method: 'POST',
        body: formData,
      })
      
      if (!response.ok) {
        throw new Error(`Transcription failed: ${response.statusText}`)
      }
      
      const data = await response.json()
      setText(prev => prev + (prev ? ' ' : '') + data.text)
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to transcribe audio')
      console.error('Transcription error:', err)
    }
  }, [])

  const startRecording = useCallback(async () => {
    try {
      // Request microphone access
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          channelCount: 1,
          sampleRate: 16000,
        } 
      })
      
      streamRef.current = stream
      
      // Create MediaRecorder
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm',
      })
      
      mediaRecorderRef.current = mediaRecorder
      chunksRef.current = []
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data)
        }
      }
      
      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(chunksRef.current, { type: 'audio/webm' })
        if (audioBlob.size > 0) {
          await sendAudioForTranscription(audioBlob)
        }
        chunksRef.current = []
      }
      
      // Start recording
      mediaRecorder.start()
      setIsRecording(true)
      setError(null)
      
      // Stop and restart every 5 seconds to get interim results
      const intervalId = setInterval(() => {
        if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
          mediaRecorderRef.current.stop()
          // Immediately start recording again
          setTimeout(() => {
            if (mediaRecorderRef.current && streamRef.current) {
              chunksRef.current = []
              mediaRecorderRef.current.start()
            }
          }, 100)
        }
      }, 5000)
      
      // Store interval ID for cleanup
      return intervalId
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start recording'
      setError(errorMessage)
      console.error('Recording error:', err)
      return null
    }
  }, [sendAudioForTranscription])

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop()
    }
    
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
    
    setIsRecording(false)
  }, [])

  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null
    
    if (active) {
      startRecording().then(id => {
        if (id) intervalId = id
      })
    } else {
      stopRecording()
    }
    
    return () => {
      if (intervalId) clearInterval(intervalId)
      stopRecording()
    }
  }, [active, startRecording, stopRecording])

  return {
    text,
    error,
    isRecording
  }
}