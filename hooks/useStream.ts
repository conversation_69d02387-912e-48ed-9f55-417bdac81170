"use client"

import { useState, useEffect, useCallback } from 'react'
import { eventBus, executeToolCall } from '@/lib/clientTools'
import { v4 as uuidv4 } from 'uuid'

export type StreamEvent = 
  | { type: 'transcript'; text: string }
  | { type: 'assistant_text'; text: string }
  | { type: 'tool_call'; name: string; args: unknown }

export interface Message {
  id: string
  role: 'user' | 'assistant' | 'tool'
  content?: string
  name?: string
  args?: any
  timestamp?: string
}

interface UseStreamOptions {
  onToolCall?: (name: string, args: any) => void
  onAssistantText?: (text: string) => void
  onTranscript?: (text: string) => void
}

export function useStream(streamUrl: string, options?: UseStreamOptions) {
  const [events, setEvents] = useState<StreamEvent[]>([])
  const [messages, setMessages] = useState<Message[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [transcript, setTranscript] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)

  // Handle incoming events from the SSE stream
  const handleEvent = useCallback(
    (event: StreamEvent) => {
      setEvents((prev) => [...prev, event])

      switch (event.type) {
        case 'transcript':
          setTranscript(event.text)
          options?.onTranscript?.(event.text)
          break

        case 'assistant_text':
          const assistantMessage: Message = {
            id: uuidv4(),
            role: 'assistant',
            content: event.text,
            timestamp: new Date().toISOString(),
          }
          setMessages((prev) => [...prev, assistantMessage])
          options?.onAssistantText?.(event.text)
          break

        case 'tool_call':
          const toolMessage: Message = {
            id: uuidv4(),
            role: 'tool',
            name: event.name,
            args: event.args,
            timestamp: new Date().toISOString(),
          }
          setMessages((prev) => [...prev, toolMessage])
          options?.onToolCall?.(event.name, event.args)
          
          // Execute the tool call
          executeToolCall({ name: event.name, arguments: event.args })
          break
      }
    },
    [options]
  )

  // Start streaming function
  const startStream = useCallback(
    async (userInput: string) => {
      try {
        setIsStreaming(true)
        setError(null)

        // Add user message to the messages list
        const userMessage: Message = {
          id: uuidv4(),
          role: 'user',
          content: userInput,
          timestamp: new Date().toISOString(),
        }
        setMessages((prev) => [...prev, userMessage])

        // Initialize EventSource for SSE
        const eventSource = new EventSource(
          `${streamUrl}?message=${encodeURIComponent(userInput)}`
        )

        eventSource.onopen = () => {
          setIsConnected(true)
        }

        // Handle different event types
        eventSource.addEventListener('transcript', (e) => {
          handleEvent({ type: 'transcript', text: e.data })
        })

        eventSource.addEventListener('assistant_text', (e) => {
          handleEvent({ type: 'assistant_text', text: e.data })
        })

        eventSource.addEventListener('tool_call', (e) => {
          try {
            const toolCall = JSON.parse(e.data)
            handleEvent({
              type: 'tool_call',
              name: toolCall.name,
              args: toolCall.arguments,
            })
          } catch (error) {
            console.error('Error parsing tool call:', error)
          }
        })

        eventSource.onerror = (e) => {
          console.error('EventSource error:', e)
          setError('Connection error with the assistant')
          eventSource.close()
          setIsConnected(false)
          setIsStreaming(false)
        }

        // Store the EventSource instance for cleanup
        return () => {
          eventSource.close()
          setIsConnected(false)
          setIsStreaming(false)
        }
      } catch (error) {
        console.error('Stream error:', error)
        setError('Failed to connect to the assistant')
        setIsStreaming(false)
        return () => {}
      }
    },
    [streamUrl, handleEvent]
  )

  // Handle tool results
  useEffect(() => {
    const handleToolResult = (data: any) => {
      const { name, args } = data
      // Update the existing tool message with the results
      setMessages((prev) => {
        const updatedMessages = [...prev]
        const toolMessageIndex = updatedMessages.findIndex(
          (msg) => msg.role === 'tool' && msg.name === name
        )
        
        if (toolMessageIndex !== -1) {
          updatedMessages[toolMessageIndex] = {
            ...updatedMessages[toolMessageIndex],
            args: { ...updatedMessages[toolMessageIndex].args, ...args },
          }
        }
        
        return updatedMessages
      })
    }

    const unsubscribe = eventBus.on('tool_result', handleToolResult)
    return unsubscribe
  }, [])

  // Handle tool errors
  useEffect(() => {
    const handleToolError = (data: any) => {
      const { name, error } = data
      setError(`Tool error: ${error}`)
    }

    const unsubscribe = eventBus.on('tool_error', handleToolError)
    return unsubscribe
  }, [])

  return {
    events,
    messages,
    isConnected,
    error,
    transcript,
    isStreaming,
    startStream,
  }
}
