"use client"

import { useEffect, useRef, useState } from "react"

interface SpeechResult {
  interim: string
  final: string
  error: string | null
  isListening: boolean
}

/**
 * Lightweight hook that streams interim speech-to-text results from the
 * browser’s built-in Web Speech API.  It is intended purely for local UI
 * feedback and does NOT send any audio to external services.
 *
 * @param active Whether recognition should be running.
 */
export default function useLocalSTT(active: boolean): SpeechResult {
  const [result, setResult] = useState({ interim: "", final: "" })
  const [error, setError] = useState<string | null>(null)
  const [isListening, setIsListening] = useState(false)
  const recogRef = useRef<SpeechRecognition | null>(null)
  // keep the latest value of `active` so event handlers always read
  // the up-to-date state instead of the one that was current when the
  // handler was first created (avoids stale-closure bugs)
  const activeRef = useRef(active)

  // Sync the ref on every change
  useEffect(() => {
    activeRef.current = active
  }, [active])

  useEffect(() => {
    // Guard: Web Speech API availability
    // @ts-ignore - vendor prefixes not in standard lib yet
    const SpeechReco = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition
    if (!SpeechReco) {
      setError('Speech recognition is not supported in this browser')
      return
    }

    if (!active) {
      // ensure ref sync and stop any existing recognition session
      activeRef.current = false
      recogRef.current?.stop()
      return
    }

    // mark as active for this session
    activeRef.current = true

    const recog: SpeechRecognition = new SpeechReco()
    recog.lang = "en-AU" // choose a locale that matches your user base
    recog.continuous = true
    recog.interimResults = true

    recog.onresult = (e: SpeechRecognitionEvent) => {
      let interim = ""
      let final = ""
      for (let i = e.resultIndex; i < e.results.length; ++i) {
        const txt = e.results[i][0].transcript
        if (e.results[i].isFinal) final += txt
        else interim += txt
      }
      setResult({ interim, final })
    }

    recog.onerror = (e: any) => {
      // Handle common errors more gracefully
      if (e.error === 'network') {
        setError('Network error - check your internet connection')
        console.warn('Speech recognition network error')
      } else if (e.error === 'no-speech') {
        // Silently ignore no-speech errors as they're expected
        return
      } else if (e.error === 'not-allowed') {
        setError('Microphone access denied - please allow microphone access')
      } else if (e.error === 'aborted') {
        // User likely stopped it, no need to log
        return
      } else {
        setError(`Speech recognition error: ${e.error || 'Unknown error'}`)
        console.error('SpeechRecognition error:', e.error || 'Unknown error')
      }
      setIsListening(false)
    }

    // Some mobile browsers stop after a while; restart automatically
    recog.onend = () => {
      // Only auto-restart if the hook is still active.  This prevents the
      // mic from quietly re-starting after the UI has toggled listening off.
      if (activeRef.current) {
        try {
          recog.start()
        } catch {
          /* ignored */
        }
      }
    }

    recog.onstart = () => {
      setIsListening(true)
      setError(null)
    }

    try {
      recog.start()
      recogRef.current = recog
    } catch (err) {
      setError('Failed to start speech recognition')
      console.error('Failed to start recognition:', err)
    }

    return () => {
      // Mark inactive before stopping so `onend` won't restart.
      activeRef.current = false
      recog.stop()
      setIsListening(false)
    }
  }, [active])

  return {
    ...result,
    error,
    isListening
  }
}
