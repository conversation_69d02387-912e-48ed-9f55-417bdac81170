'use client';

import { useCallback, useRef, useState, useEffect } from 'react';

interface AudioManagerState {
  isActive: boolean;
  activeComponent: string | null;
  stream: MediaStream | null;
  error: string | null;
}

// Global state to prevent multiple components from accessing microphone simultaneously
let globalAudioState: AudioManagerState = {
  isActive: false,
  activeComponent: null,
  stream: null,
  error: null,
};

const listeners = new Set<(state: AudioManagerState) => void>();

const logAudioEvent = (event: string, details?: any) => {
  if (process.env.NEXT_PUBLIC_ENABLE_VOICE_DEBUG === 'true') {
    console.log(`[AUDIO MANAGER] ${event}:`, details);
  }
};

const updateGlobalState = (newState: Partial<AudioManagerState>) => {
  globalAudioState = { ...globalAudioState, ...newState };
  listeners.forEach(listener => listener(globalAudioState));
  logAudioEvent('State updated', globalAudioState);
};

// Helper function to check browser compatibility
const checkBrowserCompatibility = () => {
  if (typeof window === 'undefined') {
    return { supported: false, error: 'Not in browser environment' };
  }

  if (typeof navigator === 'undefined') {
    return { supported: false, error: 'Navigator not available' };
  }

  if (!navigator.mediaDevices) {
    return {
      supported: false,
      error: 'MediaDevices API not supported. This usually means:\n• You\'re not on HTTPS (required for microphone access)\n• You\'re not on localhost\n• Your browser doesn\'t support WebRTC'
    };
  }

  if (typeof navigator.mediaDevices.getUserMedia !== 'function') {
    return {
      supported: false,
      error: 'getUserMedia not supported in this browser. Please use a modern browser like Chrome, Firefox, Safari, or Edge.'
    };
  }

  return { supported: true, error: null };
};

export const useAudioManager = (componentName: string) => {
  const [state, setState] = useState<AudioManagerState>(globalAudioState);
  const streamRef = useRef<MediaStream | null>(null);

  // Subscribe to global state changes
  useEffect(() => {
    const listener = (newState: AudioManagerState) => {
      setState(newState);
    };

    listeners.add(listener);
    return () => {
      listeners.delete(listener);
    };
  }, []);

  const requestMicrophone = useCallback(async (constraints?: MediaStreamConstraints['audio']) => {
    logAudioEvent('Microphone requested', { component: componentName, currentActive: globalAudioState.activeComponent });

    // Check browser compatibility first
    const compatibility = checkBrowserCompatibility();
    if (!compatibility.supported) {
      logAudioEvent('Browser compatibility check failed', { component: componentName, error: compatibility.error });
      updateGlobalState({ error: compatibility.error });
      throw new Error(compatibility.error);
    }

    // Check if another component is already using the microphone
    if (globalAudioState.isActive && globalAudioState.activeComponent !== componentName) {
      const error = `Microphone is already in use by ${globalAudioState.activeComponent}`;
      logAudioEvent('Request denied - already in use', { component: componentName });
      throw new Error(error);
    }

    // If we already have a stream for this component, reuse it
    if (globalAudioState.stream && globalAudioState.activeComponent === componentName) {
      logAudioEvent('Reusing existing stream', { component: componentName });
      return globalAudioState.stream;
    }

    try {
      const defaultConstraints = {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
        sampleRate: 16000,
        channelCount: 1
      };

      const stream = await navigator.mediaDevices.getUserMedia({
        audio: constraints || defaultConstraints
      });

      streamRef.current = stream;

      // Monitor track events
      stream.getTracks().forEach(track => {
        track.onended = () => {
          logAudioEvent('Track ended', { component: componentName, kind: track.kind });
          releaseMicrophone();
        };
        track.onmute = () => {
          logAudioEvent('Track muted', { component: componentName, kind: track.kind });
        };
        track.onunmute = () => {
          logAudioEvent('Track unmuted', { component: componentName, kind: track.kind });
        };
      });

      updateGlobalState({
        isActive: true,
        activeComponent: componentName,
        stream,
        error: null
      });

      logAudioEvent('Microphone granted', {
        component: componentName,
        tracks: stream.getTracks().length,
        audioTracks: stream.getAudioTracks().length
      });

      return stream;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to access microphone';
      logAudioEvent('Microphone request failed', { component: componentName, error: errorMessage });

      updateGlobalState({
        error: errorMessage
      });

      throw error;
    }
  }, [componentName]);

  const releaseMicrophone = useCallback(() => {
    logAudioEvent('Releasing microphone', { component: componentName });

    // Only release if this component is the active one
    if (globalAudioState.activeComponent !== componentName) {
      logAudioEvent('Release ignored - not active component', {
        component: componentName,
        activeComponent: globalAudioState.activeComponent
      });
      return;
    }

    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => {
        logAudioEvent('Stopping track', { component: componentName, kind: track.kind });
        track.stop();
      });
      streamRef.current = null;
    }

    if (globalAudioState.stream) {
      globalAudioState.stream.getTracks().forEach(track => {
        track.stop();
      });
    }

    updateGlobalState({
      isActive: false,
      activeComponent: null,
      stream: null,
      error: null
    });
  }, [componentName]);

  const isActiveComponent = globalAudioState.activeComponent === componentName;
  const canRequestMicrophone = !globalAudioState.isActive || isActiveComponent;

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isActiveComponent) {
        releaseMicrophone();
      }
    };
  }, [isActiveComponent, releaseMicrophone]);

  return {
    state,
    requestMicrophone,
    releaseMicrophone,
    isActiveComponent,
    canRequestMicrophone,
    stream: isActiveComponent ? globalAudioState.stream : null,
  };
};

// Export compatibility check for use in other components
export const checkMicrophoneSupport = checkBrowserCompatibility;

export default useAudioManager;
