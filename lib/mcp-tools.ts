// MCP (Model Context Protocol) Tools Definition
// This file defines available MCP tools that can be invoked through the bridge

export interface McpTool {
  name: string
  server: string
  description: string
  parameters: Record<string, any>
}

// Available MCP tools grouped by server
export const MCP_TOOLS: Record<string, McpTool[]> = {
  ElevenLabs: [
    {
      name: "text_to_speech",
      server: "ElevenLabs",
      description: "Convert text to speech with a given voice",
      parameters: {
        text: { type: "string", required: true },
        voice_name: { type: "string", required: false },
        output_directory: { type: "string", required: false },
        language: { type: "string", default: "en" },
        stability: { type: "number", default: 0.5 },
        similarity_boost: { type: "number", default: 0.75 },
        style: { type: "number", default: 0 },
        use_speaker_boost: { type: "boolean", default: true },
        speed: { type: "number", default: 1 }
      }
    },
    {
      name: "speech_to_text",
      server: "ElevenLabs",
      description: "Transcribe speech from an audio file",
      parameters: {
        input_file_path: { type: "string", required: true },
        language_code: { type: "string", default: "eng" },
        diarize: { type: "boolean", default: false },
        output_directory: { type: "string", required: false }
      }
    },
    {
      name: "text_to_sound_effects",
      server: "ElevenLabs",
      description: "Convert text description to sound effects",
      parameters: {
        text: { type: "string", required: true },
        duration_seconds: { type: "number", default: 2 },
        output_directory: { type: "string", required: false }
      }
    },
    {
      name: "search_voices",
      server: "ElevenLabs",
      description: "Search for available voices",
      parameters: {
        search: { type: "string", required: false },
        sort: { type: "string", enum: ["created_at_unix", "name"], default: "name" },
        sort_direction: { type: "string", enum: ["asc", "desc"], default: "desc" }
      }
    },
    {
      name: "get_voice",
      server: "ElevenLabs",
      description: "Get details of a specific voice",
      parameters: {
        voice_id: { type: "string", required: true }
      }
    },
    {
      name: "voice_clone",
      server: "ElevenLabs",
      description: "Clone a voice using provided audio files",
      parameters: {
        name: { type: "string", required: true },
        files: { type: "array", items: { type: "string" }, required: true },
        description: { type: "string", required: false }
      }
    },
    {
      name: "create_agent",
      server: "ElevenLabs",
      description: "Create a conversational AI agent",
      parameters: {
        name: { type: "string", required: true },
        first_message: { type: "string", required: true },
        system_prompt: { type: "string", required: true },
        voice_id: { type: "string", required: false },
        language: { type: "string", default: "en" },
        llm: { type: "string", default: "gemini-2.0-flash-001" },
        temperature: { type: "number", default: 0.5 },
        max_tokens: { type: "number", required: false }
      }
    },
    {
      name: "list_agents",
      server: "ElevenLabs",
      description: "List all available conversational AI agents",
      parameters: {}
    },
    {
      name: "get_agent",
      server: "ElevenLabs",
      description: "Get details about a specific agent",
      parameters: {
        agent_id: { type: "string", required: true }
      }
    }
  ],
  
  // Add more MCP servers and their tools here as needed
  // Example:
  // FileSystem: [
  //   {
  //     name: "read_file",
  //     server: "FileSystem",
  //     description: "Read contents of a file",
  //     parameters: {
  //       path: { type: "string", required: true }
  //     }
  //   }
  // ]
}

// Helper function to get all available MCP tools
export function getAllMcpTools(): McpTool[] {
  return Object.values(MCP_TOOLS).flat()
}

// Helper function to find a tool by name
export function findMcpTool(toolName: string): McpTool | undefined {
  return getAllMcpTools().find(tool => tool.name === toolName)
}

// Helper function to validate tool arguments
export function validateMcpToolArgs(toolName: string, args: any): { valid: boolean; errors: string[] } {
  const tool = findMcpTool(toolName)
  if (!tool) {
    return { valid: false, errors: [`Unknown tool: ${toolName}`] }
  }
  
  const errors: string[] = []
  
  // Check required parameters
  Object.entries(tool.parameters).forEach(([param, config]: [string, any]) => {
    if (config.required && !(param in args)) {
      errors.push(`Missing required parameter: ${param}`)
    }
    
    // Type validation
    if (param in args && config.type) {
      const actualType = Array.isArray(args[param]) ? 'array' : typeof args[param]
      if (actualType !== config.type) {
        errors.push(`Parameter ${param} should be ${config.type} but got ${actualType}`)
      }
    }
    
    // Enum validation
    if (param in args && config.enum && !config.enum.includes(args[param])) {
      errors.push(`Parameter ${param} must be one of: ${config.enum.join(', ')}`)
    }
  })
  
  return { valid: errors.length === 0, errors }
}

// Generate a description of all available MCP tools for the AI agent
export function generateMcpToolsDescription(): string {
  const tools = getAllMcpTools()
  const descriptions = tools.map(tool => {
    const params = Object.entries(tool.parameters)
      .map(([name, config]: [string, any]) => {
        const required = config.required ? ' (required)' : ''
        return `  - ${name}: ${config.type}${required}`
      })
      .join('\n')
    
    return `${tool.name} (${tool.server}): ${tool.description}\n${params}`
  })
  
  return `Available MCP Tools:\n\n${descriptions.join('\n\n')}`
}