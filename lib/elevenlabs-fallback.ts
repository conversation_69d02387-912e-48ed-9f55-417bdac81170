// Fallback implementation for when the ElevenLabs SDK fails to initialize

export type Role = "ai" | "user"

export interface ConversationOptions {
  onError?: (error: string) => void
  onConnect?: () => void
  onMessage?: (props: { message: string; source: Role }) => void
}

export interface ConversationInterface {
  startSession: (options: { signedUrl: string }) => Promise<void>
  endSession: () => Promise<void>
  status: "disconnected" | "connected"
  isSpeaking: boolean
}

export function createFallbackConversation(options: ConversationOptions): ConversationInterface {
  let status: "disconnected" | "connected" = "disconnected"
  const isSpeaking = false

  const startSession = async ({ signedUrl }: { signedUrl: string }) => {
    try {
      console.log("Using fallback conversation implementation")

      // Set status to connected
      status = "connected"

      // Call onConnect callback
      if (options.onConnect) {
        options.onConnect()
      }

      // Simulate a message after connection
      setTimeout(() => {
        if (options.onMessage) {
          options.onMessage({
            message: "Hello! I'm <PERSON>, your voice assistant. How can I help you today? (Fallback Mode)",
            source: "ai",
          })
        }
      }, 1000)
    } catch (error) {
      console.error("Error in fallback conversation:", error)
      if (options.onError) {
        options.onError(error instanceof Error ? error.message : String(error))
      }
    }
  }

  const endSession = async () => {
    console.log("Ending fallback session")
    status = "disconnected"
    return Promise.resolve()
  }

  return {
    startSession,
    endSession,
    get status() {
      return status
    },
    get isSpeaking() {
      return isSpeaking
    },
  }
}
