// ElevenLabs Client Tools Configuration
// This file defines the available client tools for ElevenLabs Conversational AI

export interface ClientTool {
  name: string
  description: string
  parameters: {
    type: "object"
    properties: Record<string, any>
    required?: string[]
  }
}

// Define all available client tools
export const ELEVENLABS_CLIENT_TOOLS: ClientTool[] = [
  // Email Tools
  {
    name: "openEmailDraft",
    description: "Open a new email draft window",
    parameters: {
      type: "object",
      properties: {},
    }
  },
  {
    name: "fillEmailField",
    description: "Fill a field in the email draft",
    parameters: {
      type: "object",
      properties: {
        field: {
          type: "string",
          enum: ["to", "cc", "bcc", "subject", "body", "signature"],
          description: "The field to fill"
        },
        value: {
          type: "string",
          description: "The value to set"
        }
      },
      required: ["field", "value"]
    }
  },
  {
    name: "setEmailPriority",
    description: "Set the priority level of the email",
    parameters: {
      type: "object",
      properties: {
        priority: {
          type: "string",
          enum: ["low", "normal", "high"],
          description: "Email priority level"
        }
      },
      required: ["priority"]
    }
  },
  {
    name: "addEmailAttachment",
    description: "Add an attachment to the email draft",
    parameters: {
      type: "object",
      properties: {
        fileName: {
          type: "string",
          description: "Name of the file to attach"
        },
        fileContent: {
          type: "string",
          description: "Content of the file (base64 encoded for binary files)"
        }
      },
      required: ["fileName", "fileContent"]
    }
  },
  {
    name: "sendEmail",
    description: "Send the current email draft",
    parameters: {
      type: "object",
      properties: {}
    }
  },
  
  // Browser Tools
  {
    name: "openUrl",
    description: "Open a URL in a new browser tab",
    parameters: {
      type: "object",
      properties: {
        url: {
          type: "string",
          description: "The URL to open"
        }
      },
      required: ["url"]
    }
  },
  
  // System Tools
  {
    name: "showNotification",
    description: "Show a system notification or toast message",
    parameters: {
      type: "object",
      properties: {
        title: {
          type: "string",
          description: "Notification title"
        },
        message: {
          type: "string",
          description: "Notification message"
        }
      },
      required: ["title", "message"]
    }
  },
  
  // Data Tools
  {
    name: "copyToClipboard",
    description: "Copy text to the system clipboard",
    parameters: {
      type: "object",
      properties: {
        text: {
          type: "string",
          description: "Text to copy"
        }
      },
      required: ["text"]
    }
  },
  
  // MCP Bridge
  {
    name: "invokeMcpTool",
    description: "Invoke an MCP (Model Context Protocol) tool through the bridge API",
    parameters: {
      type: "object",
      properties: {
        tool: {
          type: "string",
          description: "The MCP tool name to invoke"
        },
        args: {
          type: "object",
          description: "Arguments for the MCP tool"
        }
      },
      required: ["tool", "args"]
    }
  }
]

// Helper function to generate tool schemas for ElevenLabs
export function generateToolSchemas() {
  return ELEVENLABS_CLIENT_TOOLS.map(tool => ({
    type: "function",
    function: {
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters
    }
  }))
}

// Tool categories for organization
export const TOOL_CATEGORIES = {
  EMAIL: ["openEmailDraft", "fillEmailField", "sendEmail", "setEmailPriority", "addEmailAttachment"],
  BROWSER: ["openUrl"],
  SYSTEM: ["showNotification"],
  DATA: ["copyToClipboard"],
  MCP: ["invokeMcpTool"]
}

// Tool execution logging
export function logToolExecution(
  toolName: string,
  args: any,
  result: any,
  status: 'start' | 'success' | 'error'
) {
  const timestamp = new Date().toISOString()
  const logEntry = {
    timestamp,
    tool: toolName,
    args,
    result,
    status
  }
  
  if (status === 'error') {
    console.error('[Tool Execution Error]', logEntry)
  } else {
    console.log('[Tool Execution]', logEntry)
  }
  
  // In production, you might want to send this to an analytics service
  // or store it for debugging purposes
}