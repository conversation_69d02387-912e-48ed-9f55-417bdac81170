// Event bus for tool events
type Listener = (data: any) => void;

class EventBus {
  private listeners: Record<string, Listener[]> = {};

  on(event: string, callback: Listener) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
    return () => this.off(event, callback);
  }

  off(event: string, callback: Listener) {
    if (!this.listeners[event]) return;
    this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
  }

  emit(event: string, data: any) {
    if (!this.listeners[event]) return;
    this.listeners[event].forEach(callback => callback(data));
  }
}

export const eventBus = new EventBus();

// Mock data fetching functions
async function getWeather(city: string) {
  // In production, this would call a real weather API
  return {
    temperature: Math.round(15 + Math.random() * 15),
    condition: ["Sunny", "Partly Cloudy", "Rainy", "Overcast"][Math.floor(Math.random() * 4)],
    humidity: Math.round(40 + Math.random() * 40),
    windSpeed: Math.round(5 + Math.random() * 15),
    windDirection: ["N", "NE", "E", "SE", "S", "SW", "W", "NW"][Math.floor(Math.random() * 8)],
  };
}

async function getInspection(suburb: string) {
  // In production, this would call a real inspection rating API or database
  const establishmentNames = [
    "Corner Café", "Gourmet Kitchen", "Ocean View Restaurant", 
    "Sunrise Bakery", "The Local Diner", "Fresh Eats"
  ];
  
  const ratings = [
    { score: 95, status: 'passed' },
    { score: 88, status: 'passed' },
    { score: 62, status: 'warning' },
    { score: 45, status: 'failed' }
  ];
  
  const randomRating = ratings[Math.floor(Math.random() * ratings.length)];
  const randomEstablishment = establishmentNames[Math.floor(Math.random() * establishmentNames.length)];
  
  // Simulate some network delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  return {
    establishmentName: randomEstablishment,
    rating: randomRating.score,
    status: randomRating.status,
    inspectionDate: new Date().toLocaleDateString(),
    sourceUrl: 'https://example.com/inspections',
    sourceData: `
Inspection Report: ${randomEstablishment}
Date: ${new Date().toLocaleDateString()}
Overall Score: ${randomRating.score}/100
Status: ${randomRating.status}

Detail Breakdown:
- Food Handling: ${Math.round(70 + Math.random() * 30)}/100
- Cleanliness: ${Math.round(60 + Math.random() * 40)}/100
- Documentation: ${Math.round(75 + Math.random() * 25)}/100
- Staff Training: ${Math.round(65 + Math.random() * 35)}/100

Inspector Notes: Standard inspection carried out in accordance with
local health department regulations. ${
  randomRating.status === 'passed' 
    ? 'All major requirements satisfied.' 
    : randomRating.status === 'warning' 
      ? 'Some issues requiring attention were noted.' 
      : 'Significant issues found requiring immediate action.'
}
    `
  };
}

// Client Tools that can be called by the LLM
export async function show_weather({ city }: { city: string }) {
  try {
    const data = await getWeather(city);
    eventBus.emit('tool_result', { 
      name: 'show_weather', 
      args: { city, ...data } 
    });
    return true;
  } catch (error) {
    console.error("Error showing weather:", error);
    eventBus.emit('tool_error', { 
      name: 'show_weather',
      error: "Failed to get weather data"
    });
    return false;
  }
}

export async function show_inspection_rating({ suburb }: { suburb: string }) {
  try {
    const data = await getInspection(suburb);
    eventBus.emit('tool_result', { 
      name: 'show_inspection_rating', 
      args: { suburb, ...data } 
    });
    return true;
  } catch (error) {
    console.error("Error showing inspection rating:", error);
    eventBus.emit('tool_error', { 
      name: 'show_inspection_rating',
      error: "Failed to get inspection data"
    });
    return false;
  }
}

// Handler for executing a tool call from the LLM
export function executeToolCall(toolCall: { name: string, arguments: any }) {
  const { name, arguments: args } = toolCall;
  
  // Map of available tools
  const tools: Record<string, (args: any) => Promise<boolean>> = {
    show_weather,
    show_inspection_rating
  };
  
  if (tools[name]) {
    return tools[name](args);
  } else {
    console.error(`Unknown tool: ${name}`);
    eventBus.emit('tool_error', { 
      name,
      error: `Unknown tool: ${name}`
    });
    return Promise.resolve(false);
  }
}
