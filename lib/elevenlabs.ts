import { neon, neonConfig } from "@neondatabase/serverless"

// Configure neon to use fetch for pooling
;(neonConfig as any).poolQueryViaFetch = true
// Add a reasonable timeout
;(neonConfig as any).fetchTimeout = 10000
// Add backoff for rate limiting
;(neonConfig as any).fetchRetry = 3
;(neonConfig as any).fetchRetryMaxDelay = 2000

// Message type definition
export interface ElevenLabsMessage {
  id: string
  role: "user" | "assistant"
  content: {
    type: string
    transcript: string
  }[]
  status: string
  object: string
  type: string
}

// In-memory message store as fallback when database is unavailable
const inMemoryMessages: Record<string, any[]> = {}

// Function to save a message to the database or in-memory store
export async function saveMessage(sessionId: string, message: ElevenLabsMessage) {
  // ------------------------------------------------------------------
  // 1. Browser environment → forward to Edge API route (/api/c)
  // ------------------------------------------------------------------
  if (typeof window !== "undefined") {
    try {
      const res = await fetch("/api/c", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ id: sessionId, item: message }),
      })
      
      // Handle response
      if (res.ok) {
        const data = await res.json()
        if (!data?.error) {
          return { success: true, id: message.id, inMemory: false }
        }
      }
      
      // If we get here, the API call failed - fall through to in-memory storage
      console.warn("Database not available, using in-memory storage")
    } catch (err) {
      console.warn("saveMessage API call failed, using in-memory storage:", err)
    }
    
    // Browser-side in-memory fallback
    if (!inMemoryMessages[sessionId]) {
      inMemoryMessages[sessionId] = []
    }
    
    inMemoryMessages[sessionId].push({
      created_at: inMemoryMessages[sessionId].length,
      id: message.id,
      session_id: sessionId,
      content_type: message.content[0]?.type || "text",
      content_transcript: message.content[0]?.transcript || "",
      object: message.object || "realtime.item",
      role: message.role,
      status: message.status || "completed",
      type: message.type || "message",
      formatted: {
        text: message.content[0]?.transcript || "",
        transcript: message.content[0]?.transcript || "",
      },
    })
    
    return { success: true, id: message.id, inMemory: true }
  }

  // ------------------------------------------------------------------
  // 2. Server environment but DATABASE_URL missing → in-memory fallback
  // ------------------------------------------------------------------
  if (!process.env.DATABASE_URL) {
    console.warn("DATABASE_URL environment variable not found, using in-memory storage")

    // Initialize the session array if it doesn't exist
    if (!inMemoryMessages[sessionId]) {
      inMemoryMessages[sessionId] = []
    }

    // Add the message to the in-memory store
    inMemoryMessages[sessionId].push({
      created_at: inMemoryMessages[sessionId].length,
      id: message.id,
      session_id: sessionId,
      content_type: message.content[0]?.type || "text",
      content_transcript: message.content[0]?.transcript || "",
      object: message.object || "realtime.item",
      role: message.role,
      status: message.status || "completed",
      type: message.type || "message",
      formatted: {
        text: message.content[0]?.transcript || "",
        transcript: message.content[0]?.transcript || "",
      },
    })

    return { success: true, id: message.id, inMemory: true }
  }

  try {
    const sql = neon(process.env.DATABASE_URL)

    // Get the count of messages for this session
    const countRes = (await sql.query("SELECT COUNT(*) as count from messages WHERE session_id = $1", [sessionId])) as any
    const count = Number.parseInt(countRes.rows?.[0]?.count || "0", 10)

    // Insert the message
    await sql.query(
      "INSERT INTO messages (created_at, id, session_id, content_type, content_transcript, object, role, status, type) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) ON CONFLICT (id) DO UPDATE SET content_transcript = $5, status = $8",
      [
        count,
        message.id,
        sessionId,
        message.content[0]?.type || "text",
        message.content[0]?.transcript || "",
        message.object || "realtime.item",
        message.role,
        message.status || "completed",
        message.type || "message",
      ],
    )

    return { success: true, id: message.id, inMemory: false }
  } catch (error) {
    console.error("Error saving message to database:", error)

    // Fallback to in-memory storage
    if (!inMemoryMessages[sessionId]) {
      inMemoryMessages[sessionId] = []
    }

    inMemoryMessages[sessionId].push({
      created_at: inMemoryMessages[sessionId].length,
      id: message.id,
      session_id: sessionId,
      content_type: message.content[0]?.type || "text",
      content_transcript: message.content[0]?.transcript || "",
      object: message.object || "realtime.item",
      role: message.role,
      status: message.status || "completed",
      type: message.type || "message",
      formatted: {
        text: message.content[0]?.transcript || "",
        transcript: message.content[0]?.transcript || "",
      },
    })

    return { success: true, id: message.id, inMemory: true }
  }
}

// Function to get messages for a session from database or in-memory store
export async function getSessionMessages(sessionId: string) {
  // 1. Browser environment → query via Edge API
  if (typeof window !== "undefined") {
    try {
      const res = await fetch(`/api/c?id=${encodeURIComponent(sessionId)}`)
      if (res.ok) {
        const data = await res.json()
        if (!data?.error) {
          return data
        }
      }
      // API failed, use in-memory fallback
      console.warn("Database not available, using in-memory storage")
      return inMemoryMessages[sessionId] || []
    } catch (err) {
      console.warn("getSessionMessages API call failed, using in-memory storage:", err)
      return inMemoryMessages[sessionId] || []
    }
  }

  // 2. If DATABASE_URL is missing in server env, use in-memory store
  if (!process.env.DATABASE_URL) {
    console.warn("DATABASE_URL environment variable not found, using in-memory storage")
    return inMemoryMessages[sessionId] || []
  }

  try {
    const sql = neon(process.env.DATABASE_URL)
    const messagesRes = (await sql.query(
      "SELECT * FROM messages WHERE session_id = $1 ORDER BY created_at ASC",
      [sessionId],
    )) as any
    const messages = messagesRes.rows

    // Format messages for the UI
    return messages.map((msg: any) => ({
      ...msg,
      formatted: {
        text: msg.content_transcript,
        transcript: msg.content_transcript,
      },
    }))
  } catch (error) {
    console.error("Error getting session messages from database:", error)

    // Fallback to in-memory storage
    return inMemoryMessages[sessionId] || []
  }
}

// Function to check if database is available
export async function isDatabaseAvailable() {
  // When executed in the browser `process.env.DATABASE_URL` will be undefined.
  // In that case call the test endpoint instead of trying to connect directly.

  if (typeof window !== "undefined") {
    try {
      const res = await fetch("/api/test-db")
      if (!res.ok) return false
      const data = await res.json()
      return !!data.success
    } catch (err) {
      console.warn("Database availability check failed:", err)
      return false
    }
  }

  // Server-side check (can access env var)
  if (!process.env.DATABASE_URL) {
    return false
  }

  try {
    const sql = neon(process.env.DATABASE_URL)
    await sql.query("SELECT 1")
    return true
  } catch (error) {
    console.error("Database connection test failed:", error)
    return false
  }
}
