import { neon, neonConfig } from "@neondatabase/serverless"

// Configure neon to use fetch for pooling
;(neonConfig as any).poolQueryViaFetch = true
// Add a reasonable timeout
;(neonConfig as any).fetchTimeout = 10000
// Add backoff for rate limiting
;(neonConfig as any).fetchRetry = 3
;(neonConfig as any).fetchRetryMaxDelay = 2000

// Message type definition
export interface Message {
  created_at: number
  id: string
  session_id: string
  content_type?: string
  content_transcript?: string
  object?: string
  role: string
  status?: string
  type?: string
}

// Get database connection
export function getDb() {
  if (!process.env.DATABASE_URL) {
    throw new Error("DATABASE_URL environment variable not found")
  }
  return neon(process.env.DATABASE_URL)
}

// Create a new message
export async function createMessage(message: Message) {
  const sql = getDb()

  try {
    await sql.query(
      "INSERT INTO messages (created_at, id, session_id, content_type, content_transcript, object, role, status, type) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) ON CONFLICT (id) DO UPDATE SET content_transcript = $5, status = $8",
      [
        message.created_at,
        message.id,
        message.session_id,
        message.content_type || "text",
        message.content_transcript || "",
        message.object || "realtime.item",
        message.role,
        message.status || "completed",
        message.type || "message",
      ],
    )
    return { success: true, id: message.id }
  } catch (error) {
    console.error("Error creating message:", error)
    throw error
  }
}

// Get messages for a session
export async function getSessionMessages(sessionId: string) {
  const sql = getDb()

  try {
    const messagesRes = (await sql.query(
      "SELECT * FROM messages WHERE session_id = $1 ORDER BY created_at ASC",
      [sessionId],
    )) as any

    // The serverless driver returns an array of rows, not an object with
    // `rows` property.  Keep compatibility in case the API ever changes.
    return Array.isArray(messagesRes) ? messagesRes : messagesRes?.rows ?? []
  } catch (error) {
    console.error("Error getting session messages:", error)
    throw error
  }
}

// Update a message
export async function updateMessage(id: string, updates: Partial<Message>) {
  const sql = getDb()

  // Build the SET clause dynamically based on provided updates
  const updateFields: string[] = []
  const values: any[] = []
  let paramIndex = 1

  Object.entries(updates).forEach(([key, value]) => {
    if (key !== "id") {
      // Don't update the primary key
      updateFields.push(`${key} = $${paramIndex}`)
      values.push(value)
      paramIndex++
    }
  })

  if (updateFields.length === 0) {
    return { success: false, message: "No fields to update" }
  }

  values.push(id) // Add id as the last parameter

  try {
    await sql.query(`UPDATE messages SET ${updateFields.join(", ")} WHERE id = $${paramIndex}`, values)
    return { success: true, id }
  } catch (error) {
    console.error("Error updating message:", error)
    throw error
  }
}

// Delete a message
export async function deleteMessage(id: string) {
  const sql = getDb()

  try {
    await sql.query("DELETE FROM messages WHERE id = $1", [id])
    return { success: true, id }
  } catch (error) {
    console.error("Error deleting message:", error)
    throw error
  }
}

// Delete all messages for a session
export async function deleteSessionMessages(sessionId: string) {
  const sql = getDb()

  try {
    await sql.query("DELETE FROM messages WHERE session_id = $1", [sessionId])
    return { success: true, sessionId }
  } catch (error) {
    console.error("Error deleting session messages:", error)
    throw error
  }
}

// Get message count for a session
export async function getSessionMessageCount(sessionId: string) {
  const sql = getDb()

  try {
    const countRes = (await sql.query(
      "SELECT COUNT(*) as count FROM messages WHERE session_id = $1",
      [sessionId],
    )) as any

    const rows = Array.isArray(countRes) ? countRes : countRes?.rows ?? []
    return Number.parseInt(rows?.[0]?.count || "0", 10)
  } catch (error) {
    console.error("Error getting session message count:", error)
    throw error
  }
}
