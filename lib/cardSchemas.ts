import { z } from "zod";

/*
  Centralised Zod schemas for assistant response-cards.
  Each schema validates the `payload` section of a message coming from the AI layer.
  The message envelope recommended:
  {
    type: "InfoCard" | "SuccessAlert" | ... ,
    payload: { ...validated by respective schema }
  }
*/

export const InfoCardSchema = z.object({
  title: z.string(),
  content: z.string(),
  actions: z
    .array(
      z.object({
        label: z.string(),
        url: z.string().url().optional(),
      })
    )
    .optional(),
  tags: z.array(z.string()).optional(),
});

export const SuccessAlertSchema = z.object({
  title: z.string(),
  message: z.string(),
  actions: InfoCardSchema.shape.actions.optional(),
  tags: z.array(z.string()).optional(),
});

export const ErrorAlertSchema = SuccessAlertSchema.extend({
  // same shape, differentiate by type
});

export const ConfirmationCardSchema = z.object({
  title: z.string(),
  question: z.string(),
  confirmLabel: z.string(),
  cancelLabel: z.string(),
  tags: z.array(z.string()).optional(),
});

export const ProductCardSchema = z.object({
  title: z.string(),
  image: z.string().url(),
  price: z.number(),
  currency: z.string().default("USD"),
  rating: z.number().min(0).max(5).optional(),
  actions: InfoCardSchema.shape.actions.optional(),
  tags: z.array(z.string()).optional(),
});

export const WorkflowStepCardSchema = z.object({
  title: z.string(),
  step: z.number().int().min(1),
  totalSteps: z.number().int().min(1),
  description: z.string().optional(),
  progress: z.number().min(0).max(1).optional(),
  tags: z.array(z.string()).optional(),
});

export const DynamicFormCardSchema = z.object({
  title: z.string(),
  fields: z.array(
    z.object({
      id: z.string(),
      type: z.enum(["text", "email", "select", "checkbox", "textarea"]),
      label: z.string(),
      options: z.array(z.string()).optional(),
      required: z.boolean().optional(),
    })
  ),
  submitLabel: z.string().default("Submit"),
  tags: z.array(z.string()).optional(),
});

export const SummaryCardSchema = z.object({
  title: z.string(),
  chartType: z.enum(["bar", "pie", "line"]),
  data: z.array(
    z.object({
      label: z.string(),
      value: z.number(),
    })
  ),
  tags: z.array(z.string()).optional(),
});

export const MediaCardSchema = z.object({
  title: z.string(),
  thumbnail: z.string().url(),
  mediaUrl: z.string().url(),
  duration: z.string().optional(),
  actions: InfoCardSchema.shape.actions.optional(),
  tags: z.array(z.string()).optional(),
});

export const TaskCardSchema = z.object({
  title: z.string(),
  tasks: z.array(
    z.object({
      label: z.string(),
      done: z.boolean().default(false),
      assignee: z.string().email().optional(),
      due: z.string().optional(),
    })
  ),
  tags: z.array(z.string()).optional(),
});

export const EventCardSchema = z.object({
  title: z.string(),
  date: z.string(),
  time: z.string(),
  location: z.string(),
  description: z.string().optional(),
  actions: InfoCardSchema.shape.actions.optional(),
  tags: z.array(z.string()).optional(),
});

export const ProgressCardSchema = z.object({
  title: z.string(),
  status: z.enum(["running", "success", "error"]),
  log: z.string().optional(),
  percent: z.number().min(0).max(1).optional(),
  actions: InfoCardSchema.shape.actions.optional(),
  tags: z.array(z.string()).optional(),
});

export const cardSchemas = {
  InfoCard: InfoCardSchema,
  SuccessAlert: SuccessAlertSchema,
  ErrorAlert: ErrorAlertSchema,
  ConfirmationCard: ConfirmationCardSchema,
  ProductCard: ProductCardSchema,
  WorkflowStepCard: WorkflowStepCardSchema,
  DynamicFormCard: DynamicFormCardSchema,
  SummaryCard: SummaryCardSchema,
  MediaCard: MediaCardSchema,
  TaskCard: TaskCardSchema,
  EventCard: EventCardSchema,
  ProgressCard: ProgressCardSchema,
};

export type CardType = keyof typeof cardSchemas;
export type CardPayload<T extends CardType> = z.infer<(typeof cardSchemas)[T]>;

export function validateCard<T extends CardType>(
  type: T,
  payload: unknown
): { success: true; data: CardPayload<T> } | { success: false; error: string } {
  const schema = cardSchemas[type];
  const res = schema.safeParse(payload);
  if (res.success) return { success: true, data: res.data } as const;
  return { success: false, error: res.error.message } as const;
}
